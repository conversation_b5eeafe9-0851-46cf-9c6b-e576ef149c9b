package com.zto.intl.cts.hk.tds.service.arriveScan.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.zto.intl.common.enums.domestic.PayBillStatusEnum;
import com.zto.intl.cts.hk.commons.constant.SysConstant;
import com.zto.intl.cts.hk.commons.util.Asserts;
import com.zto.intl.cts.hk.commons.util.BeanUtil;
import com.zto.intl.cts.hk.commons.util.PageMapUtil;
import com.zto.intl.cts.hk.core.enums.ScanNoTypeEnum;
import com.zto.intl.cts.hk.core.enums.ScanTypeEnum;
import com.zto.intl.cts.hk.core.domain.web.PageQuery;
import com.zto.intl.cts.hk.core.domain.web.PageResult;
import com.zto.intl.cts.hk.core.domain.web.ValidationResult;
import com.zto.intl.cts.hk.tds.dao.entity.ArriveScan;
import com.zto.intl.cts.hk.tds.dao.mapper.ArriveScanMapper;
import com.zto.intl.cts.hk.tds.enums.DeliveryMethodEnum;
import com.zto.intl.cts.hk.tds.enums.PushStatusEnum;
import com.zto.intl.cts.hk.tds.error.ErrorEnum;
import com.zto.intl.cts.hk.tds.service.arriveScan.ArriveScanService;
import com.zto.intl.cts.hk.tds.service.arriveScan.dto.request.ArriveScanQueryReqDTO;
import com.zto.intl.cts.hk.tds.service.arriveScan.dto.request.ArriveScanReqDTO;
import com.zto.intl.cts.hk.tds.service.arriveScan.dto.request.WeightUpdateReqDTO;
import com.zto.intl.cts.hk.core.domain.web.ErrorInfo;
import com.zto.intl.cts.hk.tds.service.arriveScan.dto.response.ArriveOrderRespDTO;
import com.zto.intl.cts.hk.tds.service.arriveScan.dto.response.ArriveScanQueryRespDTO;
import com.zto.intl.cts.hk.tds.service.arriveScan.dto.response.ArriveScanRespDTO;
import com.zto.intl.cts.hk.tds.service.arriveScan.dto.response.ArriveScanCheckRespDTO;
import com.zto.intl.cts.hk.tds.service.arriveScan.dto.response.ArriveScanProcessResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 到货扫描服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ArriveScanServiceImpl implements ArriveScanService {

    private static final int MAX_SCAN_QUANTITY = 1000;
    private static final int INITIAL_BILL_COUNT = 1;

    private final ArriveScanMapper arriveScanMapper;

    /**
     * 到货扫描数据处理
     */
    @Override
    public ArriveScanRespDTO arriveScan(ArriveScanReqDTO arriveScanDTO,
                                        Map<String, ArriveOrderRespDTO> arriveOrderMap) {
        // 1. 参数校验
        validateScanRequest(arriveScanDTO);

        // 2. 获取历史扫描记录
        Map<String, ArriveScan> existingScanMap = getExistingScanMap(arriveScanDTO.getNoList(), arriveScanDTO.getNoTypeEnum());

        // 3. 处理扫描数据
        ArriveScanProcessResult processResult = processScanData(arriveScanDTO, arriveOrderMap, existingScanMap);

        // 4. 保存到数据库
        saveScanResults(processResult.getScanList());

        // 5. 构建返回结果
        return buildScanResponse(processResult);
    }

    @Override
    public void updatePushResultWithMessage(String billNo, Byte pushStatus, String pushResult) {
        Example example = new Example(ArriveScan.class);
        example.createCriteria().andEqualTo(ArriveScan.BILL_CODE, billNo);

        ArriveScan arriveScan = new ArriveScan();
        arriveScan.setPushStatus(pushStatus);
        arriveScan.setPushResult(pushResult);
        arriveScan.setPushTime(new Date());
        arriveScan.setPushCount(1);
        arriveScan.setGmtModified(new Date());
        arriveScan.setModifier(SysConstant.SYSTEM);
        arriveScanMapper.updateByExampleSelective(arriveScan, example);
    }

    @Override
    public void batchUpdatePushStatus(List<String> billNos, Byte pushStatus) {
        if (CollectionUtils.isEmpty(billNos)) {
            return;
        }

        Example example = new Example(ArriveScan.class);
        example.createCriteria().andIn(ArriveScan.BILL_CODE, billNos);

        ArriveScan arriveScan = new ArriveScan();
        arriveScan.setPushStatus(pushStatus);
        arriveScan.setPushCount(0);
        arriveScan.setNextExecuteTime(new Date());
        arriveScan.setGmtModified(new Date());
        arriveScan.setModifier(SysConstant.SYSTEM);
        arriveScanMapper.updateByExampleSelective(arriveScan, example);
    }

    @Override
    public void updateWeightAndSize(WeightUpdateReqDTO weightUpdateReqDTO) {
        // 1. 参数校验
        validateWeightUpdateRequest(weightUpdateReqDTO);

        // 2. 批量更新ArriveScan表的重量和尺寸信息
        updateArriveScanWeightAndSize(weightUpdateReqDTO);
    }

    /**
     * 校验扫描请求参数
     */
    private void validateScanRequest(ArriveScanReqDTO arriveScanDTO) {
        List<String> noList = arriveScanDTO.getNoList();
        Asserts.isTrue(noList == null || noList.size() <= MAX_SCAN_QUANTITY, ErrorEnum.ArriveScanError.SCAN_QTY_LIMIT);
    }

    /**
     * 处理扫描数据
     */
    private ArriveScanProcessResult processScanData(ArriveScanReqDTO arriveScanDTO,
                                                    Map<String, ArriveOrderRespDTO> arriveOrderMap,
                                                    Map<String, ArriveScan> existingScanMap) {
        List<ArriveScan> scanList = new ArrayList<>();
        List<ArriveOrderRespDTO> successBillList = new ArrayList<>();
        Map<String, List<String>> errorMap = new HashMap<>();
        int successCount = 0;
        int failCount = 0;
        int totalBillCount = INITIAL_BILL_COUNT;

        for (String no : arriveScanDTO.getNoList()) {
            // 创建扫描记录
            ArriveScan arriveScan = createArriveScan(arriveScanDTO, no);

            // 校验并填充数据
            ValidationResult validationResult = validateAndPopulateScan(arriveScanDTO, no, arriveScan, arriveOrderMap, existingScanMap);

            // 统计结果
            if (validationResult.isValid()) {
                successCount++;
                // 收集成功的运单号
                ArriveOrderRespDTO orderInfo = arriveOrderMap.get(no);
                if (orderInfo != null) {
                    successBillList.add(orderInfo);
                }
                // 累计运单件数
                totalBillCount += calculateBillCount(orderInfo);
                // 成功的记录保存到数据库
                scanList.add(arriveScan);
            } else {
                failCount++;
                // 收集错误信息
                String errorMsg = validationResult.getErrorMessage();
                errorMap.computeIfAbsent(errorMsg, k -> new ArrayList<>()).add(no);

                // 如果不是订单不存在的错误，保存到数据库
                if (!ErrorEnum.ArriveScanError.NO_EXIST.getMessage().equals(errorMsg)) {
                    scanList.add(arriveScan);
                }
            }
        }

        // 构建错误信息列表
        List<ErrorInfo> errors = errorMap.entrySet().stream()
                .map(entry -> new ErrorInfo(entry.getValue(), entry.getKey()))
                .collect(Collectors.toList());

        return new ArriveScanProcessResult(scanList, successBillList, successCount, failCount, totalBillCount, errors);
    }

    /**
     * 创建到货扫描记录
     */
    private ArriveScan createArriveScan(ArriveScanReqDTO arriveScanDTO, String no) {
        ArriveScan arriveScan = new ArriveScan();
        Date currentTime = new Date();

        // 设置单号信息
        setScanNumber(arriveScan, no, arriveScanDTO.getNoTypeEnum());

        // 设置尺寸重量信息
        arriveScan.setWeight(arriveScanDTO.getWeight());
        arriveScan.setLength(arriveScanDTO.getLength());
        arriveScan.setWidth(arriveScanDTO.getWidth());
        arriveScan.setHeight(arriveScanDTO.getHeight());

        // 设置操作信息
        arriveScan.setOperateTime(currentTime);
        arriveScan.setOperator(arriveScanDTO.getOperator());
        arriveScan.setSiteCode(arriveScanDTO.getSiteCode());
        arriveScan.setSiteName(arriveScanDTO.getSiteName());
        if (Objects.equals(ScanTypeEnum.SINGLE, arriveScanDTO.getScanType())) {
            arriveScan.setScanType(1);
        } else {
            arriveScan.setScanType(2);
        }

        // 设置创建信息
        arriveScan.setGmtCreate(currentTime);
        arriveScan.setCreator(arriveScanDTO.getOperator());

        return arriveScan;
    }

    /**
     * 设置扫描单号信息
     */
    private void setScanNumber(ArriveScan arriveScan, String no, ScanNoTypeEnum noTypeEnum) {
        if (Objects.equals(ScanNoTypeEnum.CUSTOMER_ORDER_NO, noTypeEnum)) {
            arriveScan.setCustomerOrderNo(no);
        } else if (Objects.equals(ScanNoTypeEnum.BILL_CODE, noTypeEnum)) {
            arriveScan.setBillCode(no);
        }
    }

    /**
     * 计算运单件数
     */
    private int calculateBillCount(ArriveOrderRespDTO orderInfo) {
        return orderInfo != null && orderInfo.getBillCount() != null ? orderInfo.getBillCount() : 0;
    }

    /**
     * 保存扫描结果
     */
    private void saveScanResults(List<ArriveScan> scanList) {
        if (!scanList.isEmpty()) {
            arriveScanMapper.insertList(scanList);
        }
    }

    /**
     * 构建扫描响应结果
     */
    private ArriveScanRespDTO buildScanResponse(ArriveScanProcessResult processResult) {
        ArriveScanRespDTO respDTO = new ArriveScanRespDTO();
        respDTO.setSuccessCount(processResult.getSuccessCount());
        respDTO.setFailCount(processResult.getFailCount());
        respDTO.setBillCount(processResult.getTotalBillCount());
        respDTO.setSuccessOrderList(processResult.getSuccessNoList());
        respDTO.setErrors(processResult.getErrors());
        return respDTO;
    }

    /**
     * 校验并填充扫描数据
     */
    private ValidationResult validateAndPopulateScan(ArriveScanReqDTO arriveScanDTO,
                                                     String no,
                                                     ArriveScan arriveScan,
                                                     Map<String, ArriveOrderRespDTO> arriveOrderMap,
                                                     Map<String, ArriveScan> existingScanMap) {
        ArriveOrderRespDTO orderInfo = arriveOrderMap.get(no);

        // 1. 检查订单是否存在
        if (orderInfo == null) {
            setFailureResult(arriveScan, ErrorEnum.ArriveScanError.NO_EXIST.getMessage());
            return new ValidationResult(false, ErrorEnum.ArriveScanError.NO_EXIST.getMessage());
        }

        // 2. 业务规则校验
        String validationError = validateBusinessRules(no, arriveScanDTO, orderInfo, existingScanMap.get(no));
        if (validationError != null) {
            setFailureResult(arriveScan, validationError);
            return ValidationResult.failure(validationError);
        }

        // 3. 校验成功，填充数据
        populateSuccessData(arriveScan, orderInfo);

        return ValidationResult.success();
    }



    /**
     * 业务规则校验
     */
    private String validateBusinessRules(String no,
                                         ArriveScanReqDTO arriveScanDTO,
                                         ArriveOrderRespDTO orderInfo,
                                         ArriveScan existingScan) {
        // 状态机校验
        ErrorInfo errorInfo = arriveScanDTO.getErrorInfo();
        if (errorInfo != null && errorInfo.getBillNos() != null) {
            if(errorInfo.getBillNos().contains(no)) {
                return ErrorEnum.ArriveScanError.SCAN_NOT_ALLOW.getMessage();
            }
        }

        // 站点重复扫描校验
        if (isDuplicateSiteScan(existingScan, arriveScanDTO)) {
            return ErrorEnum.ArriveScanError.REPEAT_SCAN.getMessage();
        }

        // 普罗托斯订单必须称重
        if (isPltsOrderRequireWeight(orderInfo, arriveScanDTO)) {
            return ErrorEnum.ArriveScanError.PLTS_REQUIRE_WEIGHT.getMessage();
        }

        return null;
    }

    /**
     * 检查是否为普罗托斯订单且需要称重
     */
    private boolean isPltsOrderRequireWeight(ArriveOrderRespDTO orderInfo, ArriveScanReqDTO arriveScanDTO) {
        BigDecimal weight = arriveScanDTO != null ? arriveScanDTO.getWeight() : null;
        return isPltsOrderRequireWeightWithWeight(orderInfo, weight);
    }

    /**
     * 检查是否为普罗托斯订单且需要称重（通用方法）
     */
    private boolean isPltsOrderRequireWeightWithWeight(ArriveOrderRespDTO orderInfo, BigDecimal weight) {
        return StringUtils.equalsIgnoreCase(orderInfo.getDataSource(), SysConstant.ORDER_SOURCE_PLTS)
                && weight == null;
    }

    /**
     * 检查是否为重复站点扫描
     */
    private boolean isDuplicateSiteScan(ArriveScan existingScan, ArriveScanReqDTO arriveScanDTO) {
        return isDuplicateSiteScanWithSiteCode(existingScan, arriveScanDTO.getSiteCode());
    }

    /**
     * 检查是否为重复站点扫描（通用方法）
     */
    private boolean isDuplicateSiteScanWithSiteCode(ArriveScan existingScan, String siteCode) {
        return existingScan != null
                && existingScan.getIsSuccess()
                && StringUtils.equalsIgnoreCase(existingScan.getSiteCode(), siteCode);
    }


    /**
     * 获取用于比较的重量
     * 优先级：结算重 > 仓库重 > 预报重
     */
    private BigDecimal getCompareWeight(ArriveOrderRespDTO orderInfo) {
        // 1. 优先使用结算重
        if (orderInfo.getChargeWeight() != null && orderInfo.getChargeWeight().compareTo(BigDecimal.ZERO) > 0) {
            return orderInfo.getChargeWeight();
        }

        // 2. 其次使用仓库重
        if (orderInfo.getWarehouseWeight() != null && orderInfo.getWarehouseWeight().compareTo(BigDecimal.ZERO) > 0) {
            return orderInfo.getWarehouseWeight();
        }

        // 3. 最后使用预报重
        if (orderInfo.getForecastWeight() != null && orderInfo.getForecastWeight().compareTo(BigDecimal.ZERO) > 0) {
            return orderInfo.getForecastWeight();
        }
        return null;
    }

    /**
     * 设置失败结果
     */
    private void setFailureResult(ArriveScan arriveScan, String failMessage) {
        arriveScan.setIsSuccess(false);
        arriveScan.setFailMessage(failMessage);
        // 失败的记录设置为不推送状态
        arriveScan.setPushStatus(PushStatusEnum.NOT_PUSH.getCode());
    }

    /**
     * 填充成功数据
     */
    private void populateSuccessData(ArriveScan arriveScan, ArriveOrderRespDTO orderInfo) {
        arriveScan.setIsSuccess(true);
        arriveScan.setBillCode(orderInfo.getBillCode());
        arriveScan.setCustomerOrderNo(orderInfo.getCustomerOrderNo());

        // 设置支付状态
        setPaymentStatus(arriveScan, orderInfo.getPayStatus());

        // 设置收件人信息
        setConsigneeInfo(arriveScan, orderInfo);

        // 设置派送方式
        setDeliveryMethod(arriveScan, orderInfo.getDeliveryMethod());

        // 设置初始推送状态：待推送
        arriveScan.setPushStatus(PushStatusEnum.WAIT_PUSH.getCode());
    }

    /**
     * 设置支付状态
     */
    private void setPaymentStatus(ArriveScan arriveScan, Byte payStatus) {
        PayBillStatusEnum payBillStatusEnum = PayBillStatusEnum.getEnum(payStatus);
        if(payBillStatusEnum != null) {
            arriveScan.setPayStatus(payBillStatusEnum.getCode());
        }
    }

    /**
     * 设置收件人信息
     */
    private void setConsigneeInfo(ArriveScan arriveScan, ArriveOrderRespDTO orderInfo) {
        arriveScan.setConsigneeName(orderInfo.getConsigneeName());
        arriveScan.setConsigneeMobile(orderInfo.getConsigneeMobile());
        arriveScan.setConsigneeAddress(orderInfo.getConsigneeAddress());
    }

    /**
     * 设置派送方式
     */
    private void setDeliveryMethod(ArriveScan arriveScan, String deliveryMethod) {
        DeliveryMethodEnum deliveryMethodEnum = DeliveryMethodEnum.getByOmsCode(deliveryMethod);
        if (deliveryMethodEnum != null) {
            arriveScan.setDeliveryMethod(deliveryMethodEnum.getCode());
        }
    }

    /**
     * 查询到货扫描数据
     */
    public List<ArriveScan> queryArriveScanList(List<String> noList, ScanNoTypeEnum noTypeEnum) {
        Example example = new Example(ArriveScan.class);
        if (Objects.equals(ScanNoTypeEnum.CUSTOMER_ORDER_NO, noTypeEnum)) {
            example.createCriteria().andIn(ArriveScan.CUSTOMER_ORDER_NO, noList);
        } else if (Objects.equals(ScanNoTypeEnum.BILL_CODE, noTypeEnum)) {
            example.createCriteria().andIn(ArriveScan.BILL_CODE, noList);
        }
        return arriveScanMapper.selectByExample(example);
    }

    /**
     * 获取已存在的扫描记录映射
     */
    public Map<String, ArriveScan> getExistingScanMap(List<String> noList, ScanNoTypeEnum noTypeEnum) {
        List<ArriveScan> existingScans = queryArriveScanList(noList, noTypeEnum);

        if (Objects.equals(ScanNoTypeEnum.CUSTOMER_ORDER_NO, noTypeEnum)) {
            return existingScans.stream()
                    .collect(Collectors.toMap(
                            ArriveScan::getCustomerOrderNo,
                            Function.identity(),
                            (existing, replacement) -> existing));
        } else if (Objects.equals(ScanNoTypeEnum.BILL_CODE, noTypeEnum)) {
            return existingScans.stream()
                    .collect(Collectors.toMap(
                            ArriveScan::getBillCode,
                            Function.identity(),
                            (existing, replacement) -> existing));
        }

        return new HashMap<>();
    }

    /**
     * 校验重量更新请求参数
     */
    private void validateWeightUpdateRequest(WeightUpdateReqDTO weightUpdateReqDTO) {
        List<String> noList = weightUpdateReqDTO.getNoList();

        Asserts.isTrue(CollectionUtils.isNotEmpty(noList), ErrorEnum.CommonError.PARAMETER_ERROR);
        Asserts.isTrue(noList.size() <= MAX_SCAN_QUANTITY, ErrorEnum.ArriveScanError.SCAN_QTY_LIMIT);
        Asserts.isTrue(weightUpdateReqDTO.getWeight() != null, ErrorEnum.CommonError.PARAMETER_ERROR);
    }

    /**
     * 批量更新ArriveScan表的重量和尺寸信息
     */
    private void updateArriveScanWeightAndSize(WeightUpdateReqDTO weightUpdateReqDTO) {
        Example example = new Example(ArriveScan.class);

        // 根据单号类型构建查询条件
        if (Objects.equals(ScanNoTypeEnum.CUSTOMER_ORDER_NO, weightUpdateReqDTO.getNoTypeEnum())) {
            example.createCriteria().andIn(ArriveScan.CUSTOMER_ORDER_NO, weightUpdateReqDTO.getNoList());
        } else if (Objects.equals(ScanNoTypeEnum.BILL_CODE, weightUpdateReqDTO.getNoTypeEnum())) {
            example.createCriteria().andIn(ArriveScan.BILL_CODE, weightUpdateReqDTO.getNoList());
        }

        // 构建更新对象
        ArriveScan updateRecord = new ArriveScan();
        updateRecord.setWeight(weightUpdateReqDTO.getWeight());
        updateRecord.setLength(weightUpdateReqDTO.getLength());
        updateRecord.setWidth(weightUpdateReqDTO.getWidth());
        updateRecord.setHeight(weightUpdateReqDTO.getHeight());
        updateRecord.setGmtModified(new Date());
        updateRecord.setModifier(weightUpdateReqDTO.getOperator());

        // 执行批量更新
        int updateCount = arriveScanMapper.updateByExampleSelective(updateRecord, example);
        log.info("批量更新ArriveScan重量尺寸完成，更新记录数：{}", updateCount);
    }

    @Override
    public PageResult<ArriveScanQueryRespDTO> queryArriveScanResult(PageQuery<ArriveScanQueryReqDTO> pageQuery) {
        // 1. 构建查询条件
        Example example = buildQueryExample(pageQuery.getCondition());

        // 2. 分页查询
        Page<ArriveScan> page = PageHelper.startPage(pageQuery.getPageNum(), pageQuery.getPageSize(), true);
        arriveScanMapper.selectByExample(example);

        // 3. 转换结果并构建分页响应
        return PageMapUtil.map(page, this::convertToQueryRespDTO);
    }

    /**
     * 构建查询条件
     */
    private Example buildQueryExample(ArriveScanQueryReqDTO queryReq) {
        Example example = new Example(ArriveScan.class);
        Example.Criteria criteria = example.createCriteria();

        // 时间范围查询
        if (queryReq.getStartTime() != null) {
            criteria.andGreaterThanOrEqualTo(ArriveScan.OPERATE_TIME, queryReq.getStartTime());
        }
        if (queryReq.getEndTime() != null) {
            criteria.andLessThanOrEqualTo(ArriveScan.OPERATE_TIME, queryReq.getEndTime());
        }
        // 支付状态查询
        if (queryReq.getPayStatus() != null) {
            criteria.andEqualTo(ArriveScan.PAY_STATUS, queryReq.getPayStatus());
        }
        // 扫描结果查询
        if (queryReq.getScanResult() != null) {
            criteria.andEqualTo(ArriveScan.IS_SUCCESS, queryReq.getScanResult());
        }
        // ids
        if (CollectionUtils.isNotEmpty(queryReq.getIds())) {
            criteria.andIn(ArriveScan.ID, queryReq.getIds());
        }

        // 按操作时间倒序排列
        example.orderBy(ArriveScan.OPERATE_TIME).desc();

        return example;
    }

    /**
     * 转换ArriveScan为ArriveScanQueryRespDTO
     */
    private ArriveScanQueryRespDTO convertToQueryRespDTO(ArriveScan arriveScan) {
        ArriveScanQueryRespDTO respDTO = new ArriveScanQueryRespDTO();

        // 复制相同名称和类型的字段
        BeanUtil.copyProperties(arriveScan, respDTO);

        // 处理需要特殊转换的字段（描述字段）
        respDTO.setPayStatusDesc(getPayStatusDesc(arriveScan.getPayStatus()));
        respDTO.setDeliveryMethodDesc(getDeliveryMethodDesc(arriveScan.getDeliveryMethod()));
        respDTO.setScanResultDesc(getScanResultDesc(arriveScan.getIsSuccess()));
        respDTO.setPushStatusDesc(getPushStatusDesc(arriveScan.getPushStatus()));

        return respDTO;
    }

    /**
     * 获取支付状态描述
     */
    private String getPayStatusDesc(Byte payStatus) {
        if (payStatus == null) {
            return "";
        }
        PayBillStatusEnum payBillStatusEnum = PayBillStatusEnum.getEnum(payStatus);
        return payBillStatusEnum != null ? payBillStatusEnum.getRemark() : "";
    }

    /**
     * 获取派送方式描述
     */
    private String getDeliveryMethodDesc(Integer deliveryMethod) {
        if (deliveryMethod == null) {
            return "";
        }
        DeliveryMethodEnum deliveryMethodEnum = DeliveryMethodEnum.getByCode(deliveryMethod);
        return deliveryMethodEnum != null ? deliveryMethodEnum.getText() : "";
    }

    /**
     * 获取扫描结果描述
     */
    private String getScanResultDesc(Boolean isSuccess) {
        if (isSuccess == null) {
            return "";
        }
        return isSuccess ? SysConstant.SUCCESS_DESC : SysConstant.FAIL_DESC;
    }

    /**
     * 获取推送状态描述
     */
    private String getPushStatusDesc(Byte pushStatus) {
        if (pushStatus == null) {
            return "";
        }
        PushStatusEnum pushStatusEnum = PushStatusEnum.getByCode(pushStatus);
        return pushStatusEnum != null ? pushStatusEnum.getText() : "";
    }

    @Override
    public ArriveScanCheckRespDTO checkBillCodes(List<String> noList, ScanNoTypeEnum noTypeEnum, BigDecimal weight,
                                                 Map<String, ArriveOrderRespDTO> orderInfoMap, ScanTypeEnum scanType,
                                                 String siteCode, ErrorInfo errorInfo) {
        return performBillCodesCheck(noList, noTypeEnum, orderInfoMap, weight, scanType, siteCode, errorInfo);
    }

    @Override
    public List<ArriveScan> queryPendingPushRecords(int limit) {
        Example example = new Example(ArriveScan.class);
        Example.Criteria criteria = example.createCriteria();

        // 查询条件：push_status = 1(待推送) 或 push_status = 3(推送失败)
        criteria.andIn(ArriveScan.PUSH_STATUS, Arrays.asList(
                PushStatusEnum.WAIT_PUSH.getCode(),
                PushStatusEnum.PUSH_FAIL.getCode()
        ));

        // 推送次数小于5次
        criteria.andLessThan(ArriveScan.PUSH_COUNT, 5);

        // 下次执行时间小于等于当前时间
        criteria.andLessThanOrEqualTo(ArriveScan.NEXT_EXECUTE_TIME, new Date());

        // 按下次执行时间升序排列
        example.orderBy(ArriveScan.NEXT_EXECUTE_TIME).asc();

        // 使用PageHelper限制查询数量
        PageHelper.startPage(1, limit, false);
        return arriveScanMapper.selectByExample(example);
    }

    @Override
    public void updatePushInfo(Long id, Integer pushCount, Date nextExecuteTime, Byte pushStatus, String pushResult) {
        ArriveScan arriveScan = new ArriveScan();
        arriveScan.setId(id);
        arriveScan.setPushCount(pushCount);
        arriveScan.setNextExecuteTime(nextExecuteTime);
        arriveScan.setPushStatus(pushStatus);
        arriveScan.setPushResult(pushResult);
        arriveScan.setPushTime(new Date());
        arriveScan.setGmtModified(new Date());
        arriveScan.setModifier(SysConstant.SYSTEM);

        arriveScanMapper.updateByPrimaryKeySelective(arriveScan);
    }

    /**
     * 执行单号校验逻辑
     */
    private ArriveScanCheckRespDTO performBillCodesCheck(List<String> noList,
                                                         ScanNoTypeEnum noTypeEnum,
                                                         Map<String, ArriveOrderRespDTO> orderInfoMap,
                                                         BigDecimal weight,
                                                         ScanTypeEnum scanType,
                                                         String siteCode,
                                                         ErrorInfo errorInfo) {
        ArriveScanCheckRespDTO result = new ArriveScanCheckRespDTO();
        List<ErrorInfo> errors = new ArrayList<>();
        boolean needConfirm = false;

        // 1. 检查订单不存在的情况
        List<String> notExistNos = new ArrayList<>();
        for (String no : noList) {
            if (!orderInfoMap.containsKey(no)) {
                notExistNos.add(no);
            }
        }

        if (!notExistNos.isEmpty()) {
            errors.add(new ErrorInfo(
                    notExistNos,
                    ErrorEnum.ArriveScanError.NO_EXIST.getMessage()
            ));
        }

        // 如果是单个扫描，添加 validateBusinessRules 中的校验逻辑
        if (Objects.equals(ScanTypeEnum.SINGLE, scanType)) {
            Map<String, ArriveScan> existingScanMap = getExistingScanMap(noList, noTypeEnum);

            // 对每个存在的订单进行业务规则校验
            for (Map.Entry<String, ArriveOrderRespDTO> entry : orderInfoMap.entrySet()) {
                String no = entry.getKey();
                ArriveOrderRespDTO orderInfo = entry.getValue();
                ArriveScan existingScan = existingScanMap.get(no);

                // 状态机校验（只有当errorInfo不为null时才进行）
                if (errorInfo != null && errorInfo.getBillNos() != null && errorInfo.getBillNos().contains(no)) {
                    errors.add(new ErrorInfo(
                            Collections.singletonList(no),
                            ErrorEnum.ArriveScanError.SCAN_NOT_ALLOW.getMessage()
                    ));
                    continue;
                }

                // 站点重复扫描校验（只有当siteCode不为null时才进行）
                if (siteCode != null && isDuplicateSiteScanWithSiteCode(existingScan, siteCode)) {
                    errors.add(new ErrorInfo(
                            Collections.singletonList(no),
                            ErrorEnum.ArriveScanError.REPEAT_SCAN.getMessage()
                    ));
                    continue;
                }

                // 普罗托斯订单必须称重校验
                if (isPltsOrderRequireWeightWithWeight(orderInfo, weight)) {
                    errors.add(new ErrorInfo(
                            Arrays.asList(no),
                            ErrorEnum.ArriveScanError.PLTS_REQUIRE_WEIGHT.getMessage()
                    ));
                    continue;
                }
            }
        }

        // 2. 重量差异校验（只对存在的订单进行校验，且weight不为空时才校验）
        if (weight != null) {
            List<String> weightDifferenceNos = new ArrayList<>();
            for (Map.Entry<String, ArriveOrderRespDTO> entry : orderInfoMap.entrySet()) {
                String no = entry.getKey();
                ArriveOrderRespDTO orderInfo = entry.getValue();

                Boolean weightWarning = validateWeightDifferenceForCheck(weight, orderInfo);
                if (weightWarning) {
                    weightDifferenceNos.add(no);
                    needConfirm = true;
                }
            }

            if (!weightDifferenceNos.isEmpty()) {
                errors.add(new ErrorInfo(
                    weightDifferenceNos,
                    ErrorEnum.ArriveScanError.WEIGHT_DIFFERENCE_CHECK.getMessage()
                ));
            }
        }

        result.setNeedConfirm(needConfirm);
        result.setErrors(errors);
        return result;
    }

    /**
     * 重量差异校验（用于check接口）
     */
    private Boolean validateWeightDifferenceForCheck(BigDecimal scanWeight, ArriveOrderRespDTO orderInfo) {
        BigDecimal compareWeight = getCompareWeight(orderInfo);

        // 如果没有可比较的重量，不进行校验
        if (compareWeight == null) {
            return false;
        }
        // 计算重量差异（绝对值）
        BigDecimal weightDifference = scanWeight.subtract(compareWeight).abs();
        // 重量差异阈值：5KG
        BigDecimal threshold = new BigDecimal("5.0");

        if (weightDifference.compareTo(threshold) >= 0) {
            return true;
        }

        return false;
    }



    @Override
    public void updatePayStatusByBillCode(String billCode, Byte payStatus) {
        // 构建更新条件
        Example example = new Example(ArriveScan.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(ArriveScan.BILL_CODE, billCode);
        criteria.andEqualTo(ArriveScan.IS_SUCCESS, true);

        // 构建更新对象
        ArriveScan updateRecord = new ArriveScan();
        updateRecord.setPayStatus(payStatus);
        updateRecord.setGmtModified(new Date());
        updateRecord.setModifier(SysConstant.SYSTEM);

        // 执行更新
        int updateCount = arriveScanMapper.updateByExampleSelective(updateRecord, example);

        log.info("支付状态更新完成，运单号：{}，支付状态：{}，更新记录数：{}",
                billCode, payStatus, updateCount);

    }

    /**
     * 查询已完成的到货扫描记录
     */
    public List<ArriveScan> queryArriveScan(String billNo) {
        Example example = new Example(ArriveScan.class);
        example.createCriteria()
                .andEqualTo(ArriveScan.BILL_CODE, billNo)
                .andEqualTo(ArriveScan.IS_SUCCESS, true);

        return arriveScanMapper.selectByExample(example);
    }
}
