package com.zto.intl.cts.hk.tds.service.task.trace.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.zto.intl.cts.hk.base.facade.api.web.traceAction.DubboActionMachineService;
import com.zto.intl.cts.hk.base.facade.api.web.traceAction.DubboTraceActionService;
import com.zto.intl.cts.hk.base.facade.api.web.traceAction.dto.response.DubboTraceActionRespDTO;
import com.zto.intl.cts.hk.commons.util.BeanUtil;
import com.zto.intl.cts.hk.tds.adapter.base.dto.response.AdapterActionRespDTO;
import com.zto.intl.cts.hk.tds.dao.entity.TraceTask;
import com.zto.intl.cts.hk.tds.dao.mapper.TraceTaskMapper;
import com.zto.intl.cts.hk.tds.service.task.trace.TraceTaskService;
import com.zto.intl.cts.hk.tds.service.task.trace.dto.request.TaskTraceAddReqDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 轨迹任务服务实现类
 *
 * <AUTHOR>
 * @date 2025/7/30
 * @version 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TraceTaskServiceImpl implements TraceTaskService {

    // 默认配置常量
    private static final String DEFAULT_MOBILE = "666666";
    private static final String DEFAULT_COUNTRY_NAME = "HONGKONG";
    private static final String DEFAULT_COUNTRY_CODE = "HK";
    private static final int DEFAULT_PUSH_COUNT = 0;

    private final TraceTaskMapper traceTaskMapper;
    @Reference
    DubboTraceActionService traceActionService;

    /**
     * 添加单个轨迹任务
     */
    @Override
    public void addTask(TaskTraceAddReqDTO reqDTO) {
        TraceTask traceTask = buildEnt(reqDTO);
        traceTaskMapper.insertSelective(traceTask);
    }

    /**
     * 批量添加轨迹任务
     */
    @Override
    public void addTaskBatch(List<TaskTraceAddReqDTO> reqDTOs){
        if(CollectionUtils.isEmpty(reqDTOs)) {
            return;
        }
        List<TraceTask> tasks = reqDTOs.stream().map(this::buildEnt).collect(Collectors.toList());
        traceTaskMapper.insertList(tasks);
    }


    private TraceTask buildEnt(TaskTraceAddReqDTO reqDTO){

        DubboTraceActionRespDTO action = traceActionService.getByActionCode(reqDTO.getActionCode());

        TraceTask traceTask = BeanUtil.copyProperties(reqDTO, new TraceTask());
        traceTask.setMobile(DEFAULT_MOBILE);
        traceTask.setCountry(DEFAULT_COUNTRY_NAME);
        traceTask.setCountryCode(DEFAULT_COUNTRY_CODE);
        traceTask.setNextExecuteTime(new Date());
        traceTask.setPushCount(DEFAULT_PUSH_COUNT);
        traceTask.setTraceActionCode(action.getTraceActionCode());
        traceTask.setMessage(action.getMessage());
        traceTask.setIsSuccess(Boolean.FALSE);
        traceTask.setGmtCreate(new Date());
        traceTask.setGmtModified(new Date());
        traceTask.setCreator(reqDTO.getOperator());
        traceTask.setModifier(reqDTO.getOperator());
        return traceTask;
    }
}
