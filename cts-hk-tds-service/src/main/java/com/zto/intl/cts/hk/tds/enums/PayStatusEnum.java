package com.zto.intl.cts.hk.tds.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum PayStatusEnum {

    NOT_PAID((byte) 0, "待支付"),
    PAYING((byte) 1, "支付中"),
    PAID((byte) 2, "支付成功"),
    PAYMENT_FAILURE((byte) 3, "支付失败"),
    CLOSED((byte) 4, "取消关闭"),
    TIMEOUT((byte) 35, "超时取消");

    private final Byte code;

    private final String remark;

    private static final Map<Byte, PayStatusEnum> ENUM_MAP = Arrays.stream(PayStatusEnum.values()).collect(Collectors.toMap(PayStatusEnum::getCode, Function.identity()));

    public static PayStatusEnum getEnum(Byte code) {
        return ENUM_MAP.get(code);
    }
}
