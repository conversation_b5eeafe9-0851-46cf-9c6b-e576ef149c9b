package com.zto.intl.cts.hk.tds.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum DeliveryMethodEnum {

    PS(0, "派件", "0"),
    ZT(1, "自提", "1"),
    ;

    private final Integer code;
    private final String text;
    private final String omsCode;

    public static final Map<String, DeliveryMethodEnum> omsCodeMap = new HashMap<>();
    public static final Map<Integer, DeliveryMethodEnum> codeMap = new HashMap<>();

    static {
        for (DeliveryMethodEnum value : values()) {
            omsCodeMap.put(value.omsCode, value);
            codeMap.put(value.code, value);
        }
    }

    /**
     * 通过omsCode获取DeliveryMethodEnum
     */
    public static DeliveryMethodEnum getByOmsCode(String omsCode) {
        return omsCodeMap.get(omsCode);
    }

    public static DeliveryMethodEnum getByCode(Integer code) {
        return codeMap.get(code);
    }
}
