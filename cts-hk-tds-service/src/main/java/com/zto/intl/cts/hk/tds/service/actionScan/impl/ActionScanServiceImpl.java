package com.zto.intl.cts.hk.tds.service.actionScan.impl;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.zto.intl.cts.hk.base.facade.api.web.traceAction.DubboTraceActionService;
import com.zto.intl.cts.hk.base.facade.api.web.traceAction.dto.response.DubboActionMachineRespDTO;
import com.zto.intl.cts.hk.base.facade.api.web.traceAction.dto.response.DubboTraceActionRespDTO;
import com.zto.intl.cts.hk.commons.util.Asserts;
import com.zto.intl.cts.hk.commons.util.BeanUtil;
import com.zto.intl.cts.hk.commons.util.PageMapUtil;
import com.zto.intl.cts.hk.commons.util.TransactionExecutor;
import com.zto.intl.cts.hk.core.domain.web.ErrorInfo;
import com.zto.intl.cts.hk.core.domain.web.PageQuery;
import com.zto.intl.cts.hk.core.domain.web.PageResult;
import com.zto.intl.cts.hk.core.enums.ScanNoTypeEnum;
import com.zto.intl.cts.hk.core.enums.ScanTypeEnum;
import com.zto.intl.cts.hk.tds.adapter.base.AdapterActionService;
import com.zto.intl.cts.hk.tds.adapter.base.dto.request.ActionMachineValidDTO;
import com.zto.intl.cts.hk.tds.adapter.base.dto.response.AdapterActionRespDTO;
import com.zto.intl.cts.hk.tds.adapter.oms.OmsService;
import com.zto.intl.cts.hk.tds.adapter.oms.dto.response.OmsOrderQueryRespDTO;
import com.zto.intl.cts.hk.tds.dao.entity.ActionScan;
import com.zto.intl.cts.hk.tds.dao.mapper.ActionScanMapper;
import com.zto.intl.cts.hk.tds.error.ErrorEnum;
import com.zto.intl.cts.hk.tds.service.actionScan.ActionScanService;
import com.zto.intl.cts.hk.tds.service.actionScan.dto.request.ActionScanPageReqDTO;
import com.zto.intl.cts.hk.tds.service.actionScan.dto.request.ActionScanReqDTO;
import com.zto.intl.cts.hk.tds.service.actionScan.dto.response.ActionScanPageRespDTO;
import com.zto.intl.cts.hk.tds.service.actionScan.dto.response.ActionScanRespDTO;
import com.zto.intl.cts.hk.tds.service.task.trace.TraceTaskService;
import com.zto.intl.cts.hk.tds.service.task.trace.dto.request.TaskTraceAddReqDTO;
import jdk.nashorn.internal.ir.annotations.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 状态扫描实现
 * @Date 2025/7/29
 * @Version 1.0
 */
@Service
public class ActionScanServiceImpl implements ActionScanService {

    // todo 注入方式的不同
    @Autowired
    private TraceTaskService traceTaskService;
    @Autowired
    private ActionScanMapper actionScanMapper;
    @Autowired
    private TransactionExecutor transactionExecutor;
    @Autowired
    private OmsService omsService;
    @Autowired
    AdapterActionService actionMachineService;

    @Override
    public void actionScan(ActionScanReqDTO reqDTO){

        OmsOrderQueryRespDTO order;
        if(ScanNoTypeEnum.BILL_CODE.name().equals(reqDTO.getNoTypeEnum().name())) {
            order = omsService.getByBillCode(reqDTO.getNoList().get(0));
        }else {
            order = omsService.getByCustomerOrdrNo(reqDTO.getNoList().get(0));
        }

        // 订单不存在 ：不保存记录
        Asserts.isTrue(null != order, ErrorEnum.ActionScanError.NO_EXIST);

        // 状态机校验结果
        DubboActionMachineRespDTO valid = actionMachineService.validSingle(reqDTO.getActionCode(), order.getBillStatus());

        // 封装扫描记录
        ActionScan actionScan = buildScanEnt(reqDTO, order, valid.getErrorMsg());

        // 封装轨迹信息
        TaskTraceAddReqDTO traceAddReqDTO = buildTraceEnt(actionScan);

        // 事务入库
        transactionExecutor.run(()->{
            traceTaskService.addTask(traceAddReqDTO);
            actionScanMapper.insert(actionScan);
        });

        // 抛出业务异常
        Asserts.isTrue(actionScan.getIsSuccess(), ErrorEnum.ActionScanError.ACTION_OVERRIDE);
    }

    @Override
    public ActionScanRespDTO actionScanBatch(ActionScanReqDTO reqDTO){

        boolean isBillNoScan = ScanNoTypeEnum.BILL_CODE.name().equals(reqDTO.getNoTypeEnum().name());
        // oms查询订单
        List<OmsOrderQueryRespDTO> orders;
        if(ScanNoTypeEnum.BILL_CODE.name().equals(reqDTO.getNoTypeEnum().name())) {
            orders = omsService.listByBillCodes(reqDTO.getNoList());
        }else {
            orders = omsService.listByCustomerOrdrNos(reqDTO.getNoList());
        }

        // 单号不存在的订单-- 不需要保存扫描记录
        Set<String> orderNos = orders.stream()
                .map(e -> isBillNoScan ? e.getBillCode() : e.getCustomerOrderNo())
                .collect(Collectors.toSet());

        List<String> notExist = reqDTO.getNoList();
        notExist.removeAll(orderNos);

        // todo 过滤掉不存在的
        orders = orders.stream()
                .filter(e -> !notExist.contains(isBillNoScan ? e.getBillCode() : e.getCustomerOrderNo()))
                .collect(Collectors.toList());

        // 状态机校验
        List<ActionMachineValidDTO> machineReqDTOS = orders.stream()
                .map(e -> new ActionMachineValidDTO(e.getBillStatus(), e.getBillCode()))
                .collect(Collectors.toList());
        DubboActionMachineRespDTO valid = actionMachineService.validBatch(reqDTO.getActionCode(), machineReqDTOS);

        // 封装扫描记录
        List<ActionScan> scanList = orders.stream()
                .map(e -> {
                    String failMsg = null;
                    if(null != valid.getErrorInfo()){
                        boolean contains = valid.getErrorInfo().getBillNos().contains(isBillNoScan ? e.getBillCode() : e.getCustomerOrderNo());
                        if(contains)
                        failMsg = valid.getErrorInfo().getErrorMsg();
                    }
                    return buildScanEnt(reqDTO, e, failMsg);
                }).collect(Collectors.toList());

        // 封装轨迹信息
        List<TaskTraceAddReqDTO> traceList = scanList.stream().map(e -> buildTraceEnt(e)).collect(Collectors.toList());

        // 构建失败信息
        List<ActionScan> fails = scanList.stream().filter(e -> !e.getIsSuccess()).collect(Collectors.toList());
        Map<String, List<String>> failsMessage = fails.stream()
                .collect(Collectors.groupingBy(ActionScan::getFailMessage,
                        Collectors.mapping(ActionScan::getBillCode, Collectors.toList())));
        failsMessage.put(ErrorEnum.ActionScanError.NO_EXIST.getMessage(), notExist);

        // 事务入库
        if(!scanList.isEmpty()) {
            transactionExecutor.run(() -> {
                actionScanMapper.insertList(scanList);
                traceTaskService.addTaskBatch(traceList);
            });
        }

        return new ActionScanRespDTO(scanList.size() - fails.size(), fails.size(), failsMessage);
    }

    @Override
    public PageResult<ActionScanPageRespDTO> page(PageQuery<ActionScanPageReqDTO> reqDTO) {

        Example example = new Example(ActionScan.class);
        Example.Criteria criteria = example.createCriteria();
        ActionScanPageReqDTO condition = reqDTO.getCondition();
        if(null != condition.getStartTime()) {
            criteria.andGreaterThanOrEqualTo(ActionScan.OPERATE_TIME, condition.getStartTime());
        }
        if(null != condition.getEndTime()) {
            criteria.andLessThanOrEqualTo(ActionScan.OPERATE_TIME, condition.getEndTime());
        }
        if(null != condition.getIsSuccess()) {
            criteria.andEqualTo(ActionScan.IS_SUCCESS, condition.getIsSuccess());
        }

        Page<ActionScan> page = PageHelper.startPage(reqDTO.getPageNum(), reqDTO.getPageSize(), true);
        actionScanMapper.selectByExample(example);

        return PageMapUtil.map(page, ActionScanPageRespDTO.class);

    }


    private ActionScan buildScanEnt(ActionScanReqDTO reqDTO, OmsOrderQueryRespDTO  orderInfo, String actionMachineMsg){

        // 封装记录
        ActionScan actionScan = BeanUtil.copyProperties(reqDTO, new ActionScan());
        actionScan.setBillCode(orderInfo.getBillCode());
        actionScan.setCustomerOrderNo(orderInfo.getCustomerOrderNo());


        actionScan.setIsSuccess(true);
        actionScan.setScanType(reqDTO.getScanTypeEnum().getCode());

        if(StringUtil.isNotBlank(actionMachineMsg)){
            actionScan.setIsSuccess(false);
            actionScan.setFailMessage(actionMachineMsg);
            return actionScan;
        }

        // 是否重复扫描
        if(isOverride(orderInfo.getBillCode(), orderInfo.getCustomerOrderNo(), reqDTO.getSiteCode(), reqDTO.getActionCode())){
            actionScan.setIsSuccess(false);
            actionScan.setFailMessage(ErrorEnum.ActionScanError.ACTION_OVERRIDE.getMessage());
        }

        AdapterActionRespDTO action = actionMachineService.getByActionCode(reqDTO.getActionCode());
        actionScan.setActionName(action.getActionName());
        actionScan.setTraceActionCode(action.getTraceActionCode());

        actionScan.setOperator(reqDTO.getUserCode());
        actionScan.setOperateTime(new Date());
        actionScan.setScanType(reqDTO.getScanTypeEnum().getCode());
        actionScan.setCreator(reqDTO.getUserCode());
        actionScan.setSiteCode("test");
        actionScan.setSiteName("test");
        return actionScan;
    }

    public Boolean isOverride(String billCode, String customerCode, String siteCode, String actionCode){
        Example example = new Example(ActionScan.class);
        Example.Criteria criteria = example.createCriteria()
                .andEqualTo(ActionScan.SITE_CODE, siteCode)
                .andEqualTo(ActionScan.ACTION_CODE, actionCode)
                .andEqualTo(ActionScan.IS_SUCCESS, true);
        if(StringUtil.isNotBlank(billCode)){
            criteria.andEqualTo(ActionScan.BILL_CODE, billCode);
        }
        if(StringUtil.isNotBlank(customerCode)){
            criteria.andEqualTo(ActionScan.CUSTOMER_ORDER_NO, customerCode);
        }

        List<ActionScan> dbActions = actionScanMapper.selectByExample(example);
        return !CollectionUtils.isEmpty(dbActions);
    }

    private TaskTraceAddReqDTO buildTraceEnt(ActionScan actionScan){

        TaskTraceAddReqDTO traceAddReqDTO = BeanUtil.copyProperties(actionScan, new TaskTraceAddReqDTO());

        return traceAddReqDTO;

    }
}
