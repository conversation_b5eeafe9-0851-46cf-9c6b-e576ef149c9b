package com.zto.intl.cts.hk.tds.service.outScan.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.zto.intl.common.enums.domestic.PayBillStatusEnum;
import com.zto.intl.cts.hk.commons.constant.SysConstant;
import com.zto.intl.cts.hk.commons.util.Asserts;
import com.zto.intl.cts.hk.commons.util.BeanUtil;
import com.zto.intl.cts.hk.commons.util.PageMapUtil;
import com.zto.intl.cts.hk.core.domain.web.ErrorInfo;
import com.zto.intl.cts.hk.core.domain.web.PageQuery;
import com.zto.intl.cts.hk.core.domain.web.PageResult;
import com.zto.intl.cts.hk.core.domain.web.ValidationResult;
import com.zto.intl.cts.hk.core.enums.ScanNoTypeEnum;
import com.zto.intl.cts.hk.core.enums.ScanTypeEnum;
import com.zto.intl.cts.hk.tds.dao.entity.ArriveScan;
import com.zto.intl.cts.hk.tds.dao.entity.OutScan;
import com.zto.intl.cts.hk.tds.dao.mapper.OutScanMapper;
import com.zto.intl.cts.hk.tds.error.ErrorEnum;
import com.zto.intl.cts.hk.tds.service.arriveScan.ArriveScanService;
import com.zto.intl.cts.hk.tds.service.outScan.OutScanService;
import com.zto.intl.cts.hk.tds.service.outScan.dto.request.OutScanQueryReqDTO;
import com.zto.intl.cts.hk.tds.service.outScan.dto.request.OutScanReqDTO;
import com.zto.intl.cts.hk.tds.service.outScan.dto.response.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 出货扫描服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OutScanServiceImpl implements OutScanService {

    private static final int MAX_SCAN_QUANTITY = 1000;
    private static final int INITIAL_BILL_COUNT = 1;

    private final OutScanMapper outScanMapper;
    private final ArriveScanService arriveScanService;

    /**
     * 出货扫描数据处理
     */
    @Override
    public OutScanRespDTO outScan(OutScanReqDTO outScanDTO,
                                  Map<String, OutOrderRespDTO> outOrderMap) {
        // 1. 参数校验
        validateScanRequest(outScanDTO);

        // 2. 获取历史扫描记录
        Map<String, OutScan> existingScanMap = getExistingScanMap(outScanDTO.getNoList(), outScanDTO.getNoTypeEnum());
        // 到货扫描记录
        Map<String, ArriveScan> arriveScanMap = arriveScanService.getExistingScanMap(outScanDTO.getNoList(), outScanDTO.getNoTypeEnum());
        // 3. 处理扫描数据
        OutScanProcessResult processResult = processScanData(outScanDTO, outOrderMap, existingScanMap, arriveScanMap);

        // 4. 保存到数据库
        saveScanResults(processResult.getScanList());

        // 5. 构建返回结果
        return buildScanResponse(processResult);
    }

    /**
     * 校验扫描请求参数
     */
    private void validateScanRequest(OutScanReqDTO outScanDTO) {
        List<String> noList = outScanDTO.getNoList();
        Asserts.isTrue(noList == null || noList.size() <= MAX_SCAN_QUANTITY, ErrorEnum.OutScanError.SCAN_QTY_LIMIT);
    }

    /**
     * 处理扫描数据
     */
    private OutScanProcessResult processScanData(OutScanReqDTO outScanDTO,
                                                 Map<String, OutOrderRespDTO> outOrderMap,
                                                 Map<String, OutScan> existingScanMap,
                                                 Map<String, ArriveScan> arriveScanMap) {
        List<OutScan> scanList = new ArrayList<>();
        List<OutOrderRespDTO> successBillList = new ArrayList<>();
        Map<String, List<String>> errorMap = new HashMap<>();
        int successCount = 0;
        int failCount = 0;
        int totalBillCount = INITIAL_BILL_COUNT;

        for (String no : outScanDTO.getNoList()) {
            // 创建扫描记录
            OutScan outScan = createOutScan(outScanDTO, no);

            // 校验并填充数据
            ValidationResult validationResult = validateAndPopulateScan(outScanDTO, no, outScan, outOrderMap, existingScanMap, arriveScanMap);

            // 统计结果
            if (validationResult.isValid()) {
                successCount++;
                // 收集成功的运单号
                OutOrderRespDTO orderInfo = outOrderMap.get(no);
                if (orderInfo != null) {
                    successBillList.add(orderInfo);
                }
                // 累计运单件数
                totalBillCount += calculateBillCount(orderInfo);
                // 成功的记录保存到数据库
                scanList.add(outScan);
            } else {
                failCount++;
                // 收集错误信息
                String errorMsg = validationResult.getErrorMessage();
                errorMap.computeIfAbsent(errorMsg, k -> new ArrayList<>()).add(no);

                // 如果不是订单不存在的错误，保存到数据库
                if (!ErrorEnum.OutScanError.NO_EXIST.getMessage().equals(errorMsg)) {
                    scanList.add(outScan);
                }
            }
        }

        // 构建错误信息列表
        List<ErrorInfo> errors = errorMap.entrySet().stream()
                .map(entry -> new ErrorInfo(entry.getValue(), entry.getKey()))
                .collect(Collectors.toList());

        return new OutScanProcessResult(scanList, successBillList, successCount, failCount, totalBillCount, errors);
    }

    /**
     * 创建出货扫描记录
     */
    private OutScan createOutScan(OutScanReqDTO outScanDTO, String no) {
        OutScan outScan = new OutScan();
        Date currentTime = new Date();

        // 设置单号信息
        setScanNumber(outScan, no, outScanDTO.getNoTypeEnum());

        // 设置操作信息
        outScan.setOperateTime(currentTime);
        outScan.setOperator(outScanDTO.getOperator());
        outScan.setSiteCode(outScanDTO.getSiteCode());
        outScan.setSiteName(outScanDTO.getSiteName());
        if (Objects.equals(ScanTypeEnum.SINGLE, outScanDTO.getScanType())) {
            outScan.setScanType(1);
        } else {
            outScan.setScanType(2);
        }

        // 设置创建信息
        outScan.setGmtCreate(currentTime);
        outScan.setCreator(outScanDTO.getOperator());

        return outScan;
    }

    /**
     * 设置扫描单号信息
     */
    private void setScanNumber(OutScan outScan, String no, ScanNoTypeEnum noTypeEnum) {
        if (Objects.equals(ScanNoTypeEnum.CUSTOMER_ORDER_NO, noTypeEnum)) {
            outScan.setCustomerOrderNo(no);
        } else if (Objects.equals(ScanNoTypeEnum.BILL_CODE, noTypeEnum)) {
            outScan.setBillCode(no);
        }
    }

    /**
     * 计算运单件数
     */
    private int calculateBillCount(OutOrderRespDTO orderInfo) {
        return orderInfo != null && orderInfo.getBillCount() != null ? orderInfo.getBillCount() : 0;
    }

    /**
     * 保存扫描结果
     */
    private void saveScanResults(List<OutScan> scanList) {
        if (!scanList.isEmpty()) {
            outScanMapper.insertList(scanList);
        }
    }

    /**
     * 构建扫描响应结果
     */
    private OutScanRespDTO buildScanResponse(OutScanProcessResult processResult) {
        OutScanRespDTO respDTO = new OutScanRespDTO();
        respDTO.setSuccessCount(processResult.getSuccessCount());
        respDTO.setFailCount(processResult.getFailCount());
        respDTO.setBillCount(processResult.getTotalBillCount());
        respDTO.setSuccessOrderList(processResult.getSuccessNoList());
        respDTO.setErrors(processResult.getErrors());
        return respDTO;
    }

    /**
     * 校验并填充扫描数据
     */
    private ValidationResult validateAndPopulateScan(OutScanReqDTO outScanDTO,
                                                     String no,
                                                     OutScan outScan,
                                                     Map<String, OutOrderRespDTO> outOrderMap,
                                                     Map<String, OutScan> existingScanMap,
                                                     Map<String, ArriveScan> arriveScanMap) {
        OutOrderRespDTO orderInfo = outOrderMap.get(no);

        // 1. 检查订单是否存在
        if (orderInfo == null) {
            setFailureResult(outScan, ErrorEnum.OutScanError.NO_EXIST.getMessage());
            return new ValidationResult(false, ErrorEnum.OutScanError.NO_EXIST.getMessage());
        }

        // 2. 业务规则校验
        String validationError = validateBusinessRules(no, outScanDTO, orderInfo, existingScanMap.get(no), arriveScanMap);
        if (validationError != null) {
            setFailureResult(outScan, validationError);
            return ValidationResult.failure(validationError);
        }

        // 3. 校验成功，填充数据
        populateSuccessData(outScan, orderInfo);

        return ValidationResult.success();
    }



    /**
     * 业务规则校验
     */
    private String validateBusinessRules(String no,
                                         OutScanReqDTO outScanDTO,
                                         OutOrderRespDTO orderInfo,
                                         OutScan existingScan,
                                         Map<String, ArriveScan> arriveScanMap) {
        // 状态机校验
        ErrorInfo errorInfo = outScanDTO.getErrorInfo();
        if (errorInfo != null && errorInfo.getBillNos() != null) {
            if(errorInfo.getBillNos().contains(no)) {
                return ErrorEnum.OutScanError.SCAN_NOT_ALLOW.getMessage();
            }
        }

        // 站点重复扫描校验
        if (isDuplicateSiteScan(existingScan, outScanDTO)) {
            return ErrorEnum.OutScanError.REPEAT_SCAN.getMessage();
        }

        // 校验是否完成到货扫描
        if(isArriveScan(no, arriveScanMap)) {
            return ErrorEnum.OutScanError.ARRIVE_SCAN_NO_RECORD.getMessage();
        }

        // 普罗托斯订单必须支付完成
        if (isPltsOrderPayed(orderInfo, outScanDTO, arriveScanMap)) {
            return ErrorEnum.OutScanError.PLTS_NOT_PAYED.getMessage();
        }

        return null;
    }

    /**
     * 检查普罗托斯订单是否完成支付
     */
    private boolean isPltsOrderPayed(OutOrderRespDTO orderInfo, OutScanReqDTO outScanDTO, Map<String, ArriveScan> arriveScanMap) {
        if(StringUtils.equalsIgnoreCase(orderInfo.getDataSource(), SysConstant.ORDER_SOURCE_PLTS)) {
            ArriveScan arriveScan = arriveScanMap.get(orderInfo.getBillCode());
            if(arriveScan != null
                    && arriveScan.getIsSuccess()
                    && Objects.equals(arriveScan.getPayStatus(), PayBillStatusEnum.PAID.getCode())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查是否为重复站点扫描
     */
    private boolean isDuplicateSiteScan(OutScan existingScan, OutScanReqDTO outScanDTO) {
        return existingScan != null
                && existingScan.getIsSuccess()
                && StringUtils.equalsIgnoreCase(existingScan.getSiteCode(), outScanDTO.getSiteCode());
    }


    /**
     * 校验是否完成到货扫描
     */
    private boolean isArriveScan(String no, Map<String, ArriveScan> arriveScanMap) {
        ArriveScan arriveScan = arriveScanMap.get(no);
        return arriveScan != null && arriveScan.getIsSuccess();
    }

    /**
     * 设置失败结果
     */
    private void setFailureResult(OutScan outScan, String failMessage) {
        outScan.setIsSuccess(false);
        outScan.setFailMessage(failMessage);
    }

    /**
     * 填充成功数据
     */
    private void populateSuccessData(OutScan outScan, OutOrderRespDTO orderInfo) {
        outScan.setIsSuccess(true);
        outScan.setBillCode(orderInfo.getBillCode());
        outScan.setCustomerOrderNo(orderInfo.getCustomerOrderNo());
    }


    /**
     * 查询出货扫描数据
     */
    public List<OutScan> queryOutScanList(List<String> noList, ScanNoTypeEnum noTypeEnum) {
        Example example = new Example(OutScan.class);
        if (Objects.equals(ScanNoTypeEnum.CUSTOMER_ORDER_NO, noTypeEnum)) {
            example.createCriteria().andIn(OutScan.CUSTOMER_ORDER_NO, noList);
        } else if (Objects.equals(ScanNoTypeEnum.BILL_CODE, noTypeEnum)) {
            example.createCriteria().andIn(OutScan.BILL_CODE, noList);
        }
        return outScanMapper.selectByExample(example);
    }


    @Override
    public PageResult<OutScanQueryRespDTO> queryOutScanResult(PageQuery<OutScanQueryReqDTO> pageQuery) {
        // 1. 构建查询条件
        Example example = buildQueryExample(pageQuery.getCondition());

        // 2. 分页查询
        Page<OutScan> page = PageHelper.startPage(pageQuery.getPageNum(), pageQuery.getPageSize(), true);
        outScanMapper.selectByExample(example);

        // 3. 转换结果并构建分页响应
        return PageMapUtil.map(page, this::convertToQueryRespDTO);
    }

    /**
     * 构建查询条件
     */
    private Example buildQueryExample(OutScanQueryReqDTO queryReq) {
        Example example = new Example(OutScan.class);
        Example.Criteria criteria = example.createCriteria();

        // 时间范围查询
        if (queryReq.getStartTime() != null) {
            criteria.andGreaterThanOrEqualTo(OutScan.OPERATE_TIME, queryReq.getStartTime());
        }
        if (queryReq.getEndTime() != null) {
            criteria.andLessThanOrEqualTo(OutScan.OPERATE_TIME, queryReq.getEndTime());
        }
        // 扫描结果查询
        if (queryReq.getScanResult() != null) {
            criteria.andEqualTo(OutScan.IS_SUCCESS, queryReq.getScanResult());
        }

        // 按操作时间倒序排列
        example.orderBy(OutScan.OPERATE_TIME).desc();

        return example;
    }

    /**
     * 转换OutScan为OutScanQueryRespDTO
     */
    private OutScanQueryRespDTO convertToQueryRespDTO(OutScan outScan) {
        OutScanQueryRespDTO respDTO = new OutScanQueryRespDTO();

        // 复制相同名称和类型的字段
        BeanUtil.copyProperties(outScan, respDTO);

        // 处理需要特殊转换的字段（描述字段）
        respDTO.setScanResultDesc(getScanResultDesc(outScan.getIsSuccess()));

        return respDTO;
    }

    /**
     * 获取扫描结果描述
     */
    private String getScanResultDesc(Boolean isSuccess) {
        if (isSuccess == null) {
            return "";
        }
        return isSuccess ? SysConstant.SUCCESS_DESC : SysConstant.FAIL_DESC;
    }


    @Override
    public OutScanCheckRespDTO checkBillCodes(List<String> noList,
                                              ScanNoTypeEnum noTypeEnum,
                                              Map<String, OutOrderRespDTO> orderInfoMap,
                                              ScanTypeEnum scanType,
                                              String siteCode,
                                              ErrorInfo errorInfo,
                                              Map<String, ArriveScan> arriveScanMap) {
        return performBillCodesCheck(noList, noTypeEnum, orderInfoMap, scanType, siteCode, errorInfo, arriveScanMap);
    }

    /**
     * 执行单号校验逻辑
     */
    private OutScanCheckRespDTO performBillCodesCheck(List<String> noList,
                                                      ScanNoTypeEnum noTypeEnum,
                                                      Map<String, OutOrderRespDTO> orderInfoMap,
                                                      ScanTypeEnum scanType,
                                                      String siteCode,
                                                      ErrorInfo errorInfo,
                                                      Map<String, ArriveScan> arriveScanMap) {
        OutScanCheckRespDTO result = new OutScanCheckRespDTO();
        List<ErrorInfo> errors = new ArrayList<>();
        boolean needConfirm = false;

        // 1. 检查订单不存在的情况
        List<String> notExistNos = new ArrayList<>();
        for (String no : noList) {
            if (!orderInfoMap.containsKey(no)) {
                notExistNos.add(no);
            }
        }

        if (!notExistNos.isEmpty()) {
            errors.add(new ErrorInfo(
                    notExistNos,
                    ErrorEnum.OutScanError.NO_EXIST.getMessage()
            ));
        }

        // 如果是单个扫描，添加 validateBusinessRules 中的校验逻辑
        if (Objects.equals(ScanTypeEnum.SINGLE, scanType)) {
            Map<String, OutScan> existingScanMap = getExistingScanMap(noList, noTypeEnum);

            // 对订单进行业务规则校验
            for (Map.Entry<String, OutOrderRespDTO> entry : orderInfoMap.entrySet()) {
                String no = entry.getKey();
                OutOrderRespDTO orderInfo = entry.getValue();
                OutScan existingScan = existingScanMap.get(no);

                // 状态机校验（只有当errorInfo不为null时才进行）
                if (errorInfo != null && errorInfo.getBillNos() != null && errorInfo.getBillNos().contains(no)) {
                    errors.add(new ErrorInfo(
                            Collections.singletonList(no),
                            ErrorEnum.OutScanError.SCAN_NOT_ALLOW.getMessage()
                    ));
                    continue;
                }

                // 站点重复扫描校验（只有当siteCode不为null时才进行）
                if (siteCode != null && isDuplicateSiteScanForCheck(existingScan, siteCode)) {
                    errors.add(new ErrorInfo(
                            Collections.singletonList(no),
                            ErrorEnum.OutScanError.REPEAT_SCAN.getMessage()
                    ));
                    continue;
                }

                // 校验是否完成到货扫描
                if (arriveScanMap != null && !isArriveScanForCheck(no, arriveScanMap)) {
                    errors.add(new ErrorInfo(
                            Collections.singletonList(no),
                            ErrorEnum.OutScanError.ARRIVE_SCAN_NO_RECORD.getMessage()
                    ));
                    continue;
                }

                // 普罗托斯订单必须支付完成
                if (arriveScanMap != null && !isPltsOrderPayedForCheck(orderInfo, arriveScanMap)) {
                    errors.add(new ErrorInfo(
                            Collections.singletonList(no),
                            ErrorEnum.OutScanError.PLTS_NOT_PAYED.getMessage()
                    ));
                }
            }
        }

        result.setNeedConfirm(needConfirm);
        result.setErrors(errors);
        return result;
    }

    /**
     * 检查是否为重复站点扫描（用于check接口）
     */
    private boolean isDuplicateSiteScanForCheck(OutScan existingScan, String siteCode) {
        return existingScan != null
                && existingScan.getIsSuccess()
                && StringUtils.equalsIgnoreCase(existingScan.getSiteCode(), siteCode);
    }

    /**
     * 校验是否完成到货扫描（用于check接口）
     */
    private boolean isArriveScanForCheck(String no, Map<String, ArriveScan> arriveScanMap) {
        ArriveScan arriveScan = arriveScanMap.get(no);
        return arriveScan == null || !arriveScan.getIsSuccess();
    }

    /**
     * 检查普罗托斯订单是否完成支付（用于check接口）
     */
    private boolean isPltsOrderPayedForCheck(OutOrderRespDTO orderInfo, Map<String, ArriveScan> arriveScanMap) {
        if (StringUtils.equalsIgnoreCase(orderInfo.getDataSource(), SysConstant.ORDER_SOURCE_PLTS)) {
            ArriveScan arriveScan = arriveScanMap.get(orderInfo.getBillCode());
            if (arriveScan == null
                    || !arriveScan.getIsSuccess()
                    || !Objects.equals(arriveScan.getPayStatus(), PayBillStatusEnum.PAID.getCode())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取已存在的扫描记录映射
     */
    private Map<String, OutScan> getExistingScanMap(List<String> noList, ScanNoTypeEnum noTypeEnum) {
        List<OutScan> existingScans = queryOutScanList(noList, noTypeEnum);

        if (Objects.equals(ScanNoTypeEnum.CUSTOMER_ORDER_NO, noTypeEnum)) {
            return existingScans.stream()
                    .collect(Collectors.toMap(
                            OutScan::getCustomerOrderNo,
                            Function.identity(),
                            (existing, replacement) -> existing));
        } else if (Objects.equals(ScanNoTypeEnum.BILL_CODE, noTypeEnum)) {
            return existingScans.stream()
                    .collect(Collectors.toMap(
                            OutScan::getBillCode,
                            Function.identity(),
                            (existing, replacement) -> existing));
        }

        return new HashMap<>();
    }
}
