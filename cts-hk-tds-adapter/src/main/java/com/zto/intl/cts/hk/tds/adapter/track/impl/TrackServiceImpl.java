package com.zto.intl.cts.hk.tds.adapter.track.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.zto.intl.common.response.ResponseEnvelope;
import com.zto.intl.common.util.DateUtil;
import com.zto.intl.common.util.ExceptionUtils;
import com.zto.intl.cts.hk.core.domain.external.BaseResponse;
import com.zto.intl.cts.hk.core.exception.BizException;
import com.zto.intl.cts.hk.tds.adapter.error.ErrorEnum;
import com.zto.intl.cts.hk.tds.adapter.track.TrackService;
import com.zto.intl.cts.hk.tds.adapter.track.dto.TracePushReqDTO;
import com.zto.intl.os.track.api.ITrackServiceApi;
import com.zto.intl.os.track.bo.ZtoIntlTrackInfoBO;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Description 轨迹推送
 * @Date 2025/8/3
 * @Version 1.0
 */
@Service
public class TrackServiceImpl implements TrackService {

    @Reference
    ITrackServiceApi trackServiceApi;

    @Override
    public BaseResponse push(TracePushReqDTO task) {

        try {

            ZtoIntlTrackInfoBO bo = new ZtoIntlTrackInfoBO();
            bo.setAction(task.getTraceActionCode());
            bo.setActionName(task.getActionName());
            bo.setMessage(task.getMessage());
            bo.setDeliveryContact(task.getMobile());
            bo.setDeliveryName(task.getOperator());
            // TODO 是否是运单号
            bo.setMailNo(task.getBillCode());
            bo.setTime(DateUtil.parse(task.getOperateTime()));
            // TODO 来源咋填-产品单独定义一个，2025-08-04
    //        taskToTrackTraceEnt.setTrackSource("CDCS");
            bo.setCountry(task.getCountry());
            bo.setCountryCode(task.getCountryCode());
            bo.setSiteCode(task.getSiteCode());
            bo.setSiteName(task.getSiteName());
            // TODO 日志
            ResponseEnvelope response = trackServiceApi.saveTrackInfo(bo);
            if(response.isSuccess()){
                return BaseResponse.success(null);
            }
            return BaseResponse.fail(ErrorEnum.TrackError.PUSH_FAIL.getCode(), response.getError().toString());
        }catch (Exception e){
            return BaseResponse.fail(ErrorEnum.TrackError.PUSH_FAIL.getCode(), ExceptionUtils.toShortString(e, 512));
        }
    }
}
