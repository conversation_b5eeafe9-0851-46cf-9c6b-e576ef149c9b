package com.zto.intl.cts.hk.tds.adapter.base.dto.response;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Description 操作状态
 * @Date 2025/8/5
 * @Version 1.0
 */
@Getter
@Setter
public class AdapterActionRespDTO {

    /**
     * 操作状态码
     */
    private String actionCode;

    /**
     * 操作状态描述
     */
    private String actionName;

    /**
     * 轨迹状态码
     */
    private String traceActionCode;

    /**
     * 是否推送轨迹系统，0 否 1 是
     */
    private Boolean needPush;

    /**
     * 轨迹描述
     */
    private String message;
}
