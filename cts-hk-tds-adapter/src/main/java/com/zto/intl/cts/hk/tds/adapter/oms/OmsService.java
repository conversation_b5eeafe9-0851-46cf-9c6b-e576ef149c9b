package com.zto.intl.cts.hk.tds.adapter.oms;

import com.zto.intl.cts.hk.core.domain.web.PageResult;
import com.zto.intl.cts.hk.tds.adapter.oms.dto.request.OmsOrderLabelReqDTO;
import com.zto.intl.cts.hk.tds.adapter.oms.dto.request.OmsOrderQueryReqDTO;
import com.zto.intl.cts.hk.tds.adapter.oms.dto.response.OmsOrderQueryRespDTO;
import com.zto.intl.cts.hk.tds.adapter.oms.dto.request.OmsOrderWeightReqDTO;
import com.zto.intl.cts.hk.tds.adapter.oms.dto.response.OmsUpdateWeightRespDTO;

import java.util.List;

/**
 * oms 交互服务类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/07/25 15:22
 */
// todo adapter 统一命名AdapterxxxxService
public interface OmsService {

    /**
     * 获取OMS运单信息
     */
    PageResult<OmsOrderQueryRespDTO> pageOmsOrders(OmsOrderQueryReqDTO params) ;

    /**
     * 根据客户单号批量获取OMS运单信息
     */
    List<OmsOrderQueryRespDTO> listByCustomerOrdrNos(List<String> customerOrderNos);

    /**
     * 根据运单号批量获取OMS运单信息
     */
    List<OmsOrderQueryRespDTO> listByBillCodes(List<String> billCodes);

    /**
     * 根据运单号获取运单信息
     */
    OmsOrderQueryRespDTO getByBillCode(String billCode);
    /**
     * 根据客户单号获取运单信息
     */
    OmsOrderQueryRespDTO getByCustomerOrdrNo(String customerOrderNo);

    /**
     * 推送重量尺寸
     */
    OmsUpdateWeightRespDTO pushWeightAndSize(OmsOrderWeightReqDTO weightReqDTO);

    /**
     * 获取面单
     */
    String getOmsLabel(List<OmsOrderLabelReqDTO> labelReqDTOList);
}
