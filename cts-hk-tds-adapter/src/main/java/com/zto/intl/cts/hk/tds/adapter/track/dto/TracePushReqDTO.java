package com.zto.intl.cts.hk.tds.adapter.track.dto;

import javax.persistence.Column;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description 推送轨迹
 * @Date 2025/8/3
 * @Version 1.0
 */
public class TracePushReqDTO {
    /**
     * 运单号
     */
    private String billCode;
    /**
     * 客户订单号
     */
    private String customerOrderNo;

    /**
     * 轨迹状态码
     */
    private String traceActionCode;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作状态描述
     */
    private String actionName;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 操作时间
     */
    private Date operateTime;

    /**
     * 站点编码
     */
    private String siteCode;

    /**
     * 站点名称
     */
    private String siteName;

    /**
     * 国家、地区编码
     */
    private String countryCode;

    /**
     * 国家
     */
    private String country;

    // TODO 轨迹描述是否保存一个
    /**
     * 轨迹描述
     */
    private String message;

    public String getActionName() {
        return actionName;
    }

    public void setActionName(String actionName) {
        this.actionName = actionName;
    }
    public String getBillCode() {
        return billCode;
    }

    public void setBillCode(String billCode) {
        this.billCode = billCode;
    }

    public String getCustomerOrderNo() {
        return customerOrderNo;
    }

    public void setCustomerOrderNo(String customerOrderNo) {
        this.customerOrderNo = customerOrderNo;
    }

    public String getTraceActionCode() {
        return traceActionCode;
    }

    public void setTraceActionCode(String traceActionCode) {
        this.traceActionCode = traceActionCode;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public Date getOperateTime() {
        return operateTime;
    }

    public void setOperateTime(Date operateTime) {
        this.operateTime = operateTime;
    }

    public String getSiteCode() {
        return siteCode;
    }

    public void setSiteCode(String siteCode) {
        this.siteCode = siteCode;
    }

    public String getSiteName() {
        return siteName;
    }

    public void setSiteName(String siteName) {
        this.siteName = siteName;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }



}
