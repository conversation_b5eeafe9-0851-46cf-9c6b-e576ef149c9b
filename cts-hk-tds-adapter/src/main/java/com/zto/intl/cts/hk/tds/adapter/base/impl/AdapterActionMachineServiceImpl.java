package com.zto.intl.cts.hk.tds.adapter.base.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.dubbo.rpc.RpcContext;
import com.zto.intl.cts.hk.base.facade.api.web.traceAction.DubboActionMachineService;
import com.zto.intl.cts.hk.base.facade.api.web.traceAction.DubboTraceActionService;
import com.zto.intl.cts.hk.base.facade.api.web.traceAction.dto.request.DubboActionMachineReqDTO;
import com.zto.intl.cts.hk.base.facade.api.web.traceAction.dto.response.DubboActionMachineRespDTO;
import com.zto.intl.cts.hk.base.facade.api.web.traceAction.dto.response.DubboTraceActionRespDTO;
import com.zto.intl.cts.hk.commons.util.BeanUtil;
import com.zto.intl.cts.hk.tds.adapter.base.AdapterActionService;
import com.zto.intl.cts.hk.tds.adapter.base.dto.request.ActionMachineValidDTO;
import com.zto.intl.cts.hk.tds.adapter.base.dto.response.AdapterActionRespDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 状态机校验
 * @Date 2025/8/4
 * @Version 1.0
 */
@Service
public class AdapterActionMachineServiceImpl implements AdapterActionService {

    @Reference
    DubboTraceActionService dubboTraceActionService;
    @Reference
    DubboActionMachineService actionMachineService;

    // 在类的字段区域添加配置注入
    @Value("${dubbo.tag.enabled:false}")
    private boolean dubboTagEnabled;

    @Value("${base.dubbo.tag.name:}")
    private String dubboTagName;


    public DubboActionMachineRespDTO validSingle(String actionCode, String orderStatus){
        return actionMachineService.validSingle(actionCode, orderStatus);
    }


    // TODO 响应对象映射
    public DubboActionMachineRespDTO validBatch(String actionCode, List<ActionMachineValidDTO> orders){
        setDubboTagIfEnabled();

        List<DubboActionMachineReqDTO> reqDTOS = BeanUtil.mapAsList(orders, DubboActionMachineReqDTO.class);
        return actionMachineService.validBatch(actionCode, reqDTOS);
    }


    // todo 添加新方法用于设置Dubbo标签
    private void setDubboTagIfEnabled() {
        if (dubboTagEnabled && StringUtils.isNotBlank(dubboTagName)) {
            RpcContext.getContext().setAttachment("dubbo.tag", dubboTagName);
        }
    }


    @Cacheable
//    @Cacheable
    public List<AdapterActionRespDTO> listAction(){
        List<DubboTraceActionRespDTO> dubboTraceActionRespDTOS = dubboTraceActionService.likeByCodeOrName(null);
        return BeanUtil.mapAsList(dubboTraceActionRespDTOS, AdapterActionRespDTO.class);
    }

    // TODO CACHE FAIL
//    @Cacheable
    public AdapterActionRespDTO getByActionCode(String actionCode){
        DubboTraceActionRespDTO dubboDto = dubboTraceActionService.getByActionCode(actionCode);
        return BeanUtil.copyProperties(dubboDto, new AdapterActionRespDTO());
    }
}
