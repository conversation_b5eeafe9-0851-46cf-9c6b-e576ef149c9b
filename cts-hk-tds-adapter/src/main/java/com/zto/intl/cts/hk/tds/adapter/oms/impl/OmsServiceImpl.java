package com.zto.intl.cts.hk.tds.adapter.oms.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.dubbo.rpc.RpcContext;
import com.zto.intl.common.entity.Page;
import com.zto.intl.common.response.ResponseEnvelope;
import com.zto.intl.common.response.error.ResponseError;
import com.zto.intl.cts.hk.core.domain.web.PageResult;
import com.zto.intl.cts.hk.core.exception.BizException;
import com.zto.intl.cts.hk.tds.adapter.error.ErrorEnum;
import com.zto.intl.cts.hk.tds.adapter.oms.dto.request.OmsOrderQueryReqDTO;
import com.zto.intl.cts.hk.tds.adapter.oms.dto.response.OmsOrderQueryRespDTO;
import com.zto.intl.cts.hk.tds.adapter.oms.OmsService;
import com.zto.intl.cts.hk.tds.adapter.oms.dto.request.OmsOrderLabelReqDTO;
import com.zto.intl.cts.hk.tds.adapter.oms.dto.request.OmsOrderWeightReqDTO;
import com.zto.intl.cts.hk.tds.adapter.oms.dto.response.OmsUpdateWeightRespDTO;
import com.zto.intl.overseas.facade.bo.req.hk.HkTmsQueryOrderReqBO;
import com.zto.intl.overseas.facade.bo.req.hk.PrintReqBO;
import com.zto.intl.overseas.facade.bo.req.hk.UpdateWeightReqBO;
import com.zto.intl.overseas.facade.bo.res.hk.*;
import com.zto.intl.overseas.facade.dubbo.hk.order.IHkOrderQueryApi;
import com.zto.intl.overseas.facade.dubbo.hk.order.IHkOrderUpdateApi;
import com.zto.intl.overseas.facade.dubbo.hk.order.IHkPrintApi;
import com.zto.titans.common.util.JsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * OMS 服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OmsServiceImpl implements OmsService {

    @Reference
    private IHkOrderQueryApi hkOrderQueryApi;
    @Reference
    private IHkOrderUpdateApi hkOrderUpdateApi;
    @Reference
    private IHkPrintApi hkPrintApi;

    // 在类的字段区域添加配置注入
    @Value("${dubbo.tag.enabled:false}")
    private boolean dubboTagEnabled;

    @Value("${dubbo.tag.name:}")
    private String dubboTagName;

    @Override
    public OmsOrderQueryRespDTO getByCustomerOrdrNo(String customerOrderNo) {
        HkTmsQueryOrderReqBO reqBO = new HkTmsQueryOrderReqBO();
        reqBO.setCustomerOrderNoList(Arrays.asList(customerOrderNo));
        reqBO.setPageNo(1);
        reqBO.setPageSize(1);
        List<OmsOrderQueryRespDTO> data = pageOmsOrders(reqBO).getData();
        return CollectionUtils.isNotEmpty(data)? data.get(0) : null;
    }

    @Override
    public OmsOrderQueryRespDTO getByBillCode(String billCode) {
        HkTmsQueryOrderReqBO reqBO = new HkTmsQueryOrderReqBO();
        reqBO.setBillCodeList(Arrays.asList(billCode));
        reqBO.setPageNo(1);
        reqBO.setPageSize(1);
        List<OmsOrderQueryRespDTO> data = pageOmsOrders(reqBO).getData();
        return CollectionUtils.isNotEmpty(data)? data.get(0) : null;
    }

    @Override
    public List<OmsOrderQueryRespDTO> listByCustomerOrdrNos(List<String> customerOrderNos) {

        HkTmsQueryOrderReqBO reqBO = new HkTmsQueryOrderReqBO();
        reqBO.setCustomerOrderNoList(customerOrderNos);
        reqBO.setPageNo(1);
        reqBO.setPageSize(customerOrderNos.size());
        return pageOmsOrders(reqBO).getData();
    }

    @Override
    public List<OmsOrderQueryRespDTO> listByBillCodes(List<String> billCodes) {

        HkTmsQueryOrderReqBO reqBO = new HkTmsQueryOrderReqBO();
        reqBO.setBillCodeList(billCodes);
        reqBO.setPageNo(1);
        reqBO.setPageSize(billCodes.size());
        return pageOmsOrders(reqBO).getData();
    }

    @Override
    public PageResult<OmsOrderQueryRespDTO> pageOmsOrders(OmsOrderQueryReqDTO params) {

        HkTmsQueryOrderReqBO reqBO = new HkTmsQueryOrderReqBO();
        reqBO.setCustomerOrderNoList(params.getCustomerOrderNoList());
        reqBO.setBillCodeList(params.getBillCodeList());
        reqBO.setPageNo(params.getPageNum());
        reqBO.setPageSize(params.getPageSize());

        return pageOmsOrders(reqBO);
    }


    @Override
    public OmsUpdateWeightRespDTO pushWeightAndSize(OmsOrderWeightReqDTO reqBO) {
        setDubboTagIfEnabled();

        UpdateWeightReqBO updateWeightReqBO = new UpdateWeightReqBO();
        updateWeightReqBO.setBillCode(reqBO.getBillCode());
        updateWeightReqBO.setWarehouseWeight(reqBO.getWeight());
        updateWeightReqBO.setLength(reqBO.getLength());
        updateWeightReqBO.setWidth(reqBO.getWidth());
        updateWeightReqBO.setHeight(reqBO.getHeight());

        log.info("推送oms重量信息入参:{}", JsonUtil.toJSON(reqBO));
        ResponseEnvelope<UpdateWeightResBO> omsResult = hkOrderUpdateApi.updateWeight(updateWeightReqBO);
        log.info("推送oms重量信息出参:{}", JsonUtil.toJSON(omsResult));

        // 返回数据处理
        OmsUpdateWeightRespDTO res = new OmsUpdateWeightRespDTO();
        res.setBillCode(reqBO.getBillCode());
        res.setSuccess(true);

        if (!omsResult.isSuccess()) {
            ResponseError error = omsResult.getError();
            if(error != null) {
                res.setSuccess(false);
                res.setFailReason(error.getMessage());
            }
        }

        return res;
    }

    @Override
    public String getOmsLabel(List<OmsOrderLabelReqDTO> labelReqDTOList) {
        setDubboTagIfEnabled();

        PrintReqBO printReqBO = new PrintReqBO();
        printReqBO.setBillCodeList(labelReqDTOList.stream().map(OmsOrderLabelReqDTO::getBillCode).collect(Collectors.toList()));
        // 设置面单模板代码
        String templateCode = labelReqDTOList.get(0).getTemplateCode();
        printReqBO.setTemplateCode(templateCode);

        log.info("查询oms面单信息入参:{}", JsonUtil.toJSON(printReqBO));
        ResponseEnvelope<PrintResBO> printRes = hkPrintApi.print(printReqBO);
        log.info("查询oms面单信息出参:{}", JsonUtil.toJSON(printRes));

        // 返回数据处理
        if (!printRes.isSuccess()) {
            String errorMsg = "";
            ResponseError error = printRes.getError();
            if(error != null) {
                errorMsg = error.getMessage();
            }
           throw new BizException(ErrorEnum.OmsError.OMS_LABEL_GET_ERROR, errorMsg);
        }

        return printRes.getData().getUrl();
    }

    private PageResult<OmsOrderQueryRespDTO> pageOmsOrders(HkTmsQueryOrderReqBO reqBO) {
        setDubboTagIfEnabled();

        log.info("查询oms运单信息入参:{}", JsonUtil.toJSON(reqBO));

        ResponseEnvelope<Page<HkOrderInfoResBO>> omsResult = hkOrderQueryApi.tmsQuery(reqBO);
        log.info("查询oms运单信息出参:{}", JsonUtil.toJSON(omsResult));

        // 返回数据处理
        if (!omsResult.isSuccess()) {
            return new PageResult<>();
        }
        return mapOmsData(omsResult.getData());
    }

    /** 映射OMS数据List */
    private PageResult<OmsOrderQueryRespDTO> mapOmsData(Page<HkOrderInfoResBO> omsQueryResData) {
        final List<OmsOrderQueryRespDTO> mappingData = transferOmsResData(omsQueryResData.getData());
        final PageResult<OmsOrderQueryRespDTO> result = new PageResult<>();
        result.setPageNum(omsQueryResData.getPageNo());
        result.setPages(omsQueryResData.getTotalPage());
        result.setPageSize(omsQueryResData.getPageSize());
        result.setTotal(omsQueryResData.getTotalItem());
        result.setData(mappingData);

        return result;
    }

    /** 映射OMS数据 */
    private List<OmsOrderQueryRespDTO> transferOmsResData(List<HkOrderInfoResBO> omsQueryResList) {
        final List<HkOrderInfoResBO> omsDataList = (omsQueryResList == null) ? Collections.emptyList() : omsQueryResList;
        if(CollectionUtils.isEmpty(omsDataList)) {
            return Collections.emptyList();
        }

        return omsDataList.stream().map(omsResData -> {
            OmsOrderQueryRespDTO omsOrderQueryRespDTO = new OmsOrderQueryRespDTO();
            omsOrderQueryRespDTO.setBillCode(omsResData.getBillCode());
            omsOrderQueryRespDTO.setCustomerOrderNo(omsResData.getCustomerOrderNo());
            omsOrderQueryRespDTO.setPayStatus(omsResData.getPayStatus());
            omsOrderQueryRespDTO.setDeliveryMethod(omsResData.getDeliveryType());

            // 收件人信息
            HkOrderReceiverResBO receiver = omsResData.getReceiverRes();
            omsOrderQueryRespDTO.setConsigneeName(receiver.getName());
            omsOrderQueryRespDTO.setConsigneeMobile(receiver.getMobile());
            omsOrderQueryRespDTO.setConsigneeAddress(receiver.getAddress());
            // 商品总件数
            omsOrderQueryRespDTO.setBillCount(omsResData.getPackageQty()==null ? 0:omsResData.getPackageQty());
            // 重量
            HkOrderWeightResBO weightRes = omsResData.getWeightRes();
            if(weightRes != null) {
                omsOrderQueryRespDTO.setChargeWeight(weightRes.getChargeWeight());
                omsOrderQueryRespDTO.setWarehouseWeight(weightRes.getWarehouseWeight());
                omsOrderQueryRespDTO.setForecastWeight(weightRes.getGrossWeight());
            }
            omsOrderQueryRespDTO.setDataSource(omsResData.getDataSource());
            omsOrderQueryRespDTO.setBillStatus(omsResData.getBillStatus());

            // todo oms没返回先写死
            omsOrderQueryRespDTO.setBillStatus("01");
            return omsOrderQueryRespDTO;
        }).collect(Collectors.toList());
    }


    // 添加新方法用于设置Dubbo标签
    private void setDubboTagIfEnabled() {
        if (dubboTagEnabled && StringUtils.isNotBlank(dubboTagName)) {
            RpcContext.getContext().setAttachment("dubbo.tag", dubboTagName);
        }
    }
}
