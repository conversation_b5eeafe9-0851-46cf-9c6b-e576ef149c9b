package com.zto.intl.cts.hk.tds.provider.impl.web.outScan;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.zto.intl.cts.hk.base.facade.api.web.traceAction.dto.response.DubboActionMachineRespDTO;
import com.zto.intl.cts.hk.commons.util.BeanUtil;
import com.zto.intl.cts.hk.core.domain.web.ErrorInfo;
import com.zto.intl.cts.hk.core.domain.web.PageQuery;
import com.zto.intl.cts.hk.core.domain.web.PageResult;
import com.zto.intl.cts.hk.core.enums.ScanNoTypeEnum;
import com.zto.intl.cts.hk.core.enums.ScanTypeEnum;
import com.zto.intl.cts.hk.tds.adapter.base.ActionMachineService;
import com.zto.intl.cts.hk.tds.adapter.base.dto.request.ActionMachineValidDTO;
import com.zto.intl.cts.hk.tds.adapter.oms.OmsService;
import com.zto.intl.cts.hk.tds.adapter.oms.dto.response.OmsOrderQueryRespDTO;
import com.zto.intl.cts.hk.tds.enums.ActionCodeEnum;
import com.zto.intl.cts.hk.tds.enums.TraceEnum;
import com.zto.intl.cts.hk.tds.facade.api.web.outScan.IDubboOutScanService;
import com.zto.intl.cts.hk.tds.facade.api.web.outScan.dto.request.DubboOutScanCheckReqDTO;
import com.zto.intl.cts.hk.tds.facade.api.web.outScan.dto.request.DubboOutScanQueryReqDTO;
import com.zto.intl.cts.hk.tds.facade.api.web.outScan.dto.request.DubboOutScanReqDTO;
import com.zto.intl.cts.hk.tds.facade.api.web.outScan.dto.response.DubboOutScanCheckRespDTO;
import com.zto.intl.cts.hk.tds.facade.api.web.outScan.dto.response.DubboOutScanQueryRespDTO;
import com.zto.intl.cts.hk.tds.facade.api.web.outScan.dto.response.DubboOutScanRespDTO;
import com.zto.intl.cts.hk.tds.service.outScan.OutScanService;
import com.zto.intl.cts.hk.tds.service.outScan.dto.request.OutScanQueryReqDTO;
import com.zto.intl.cts.hk.tds.service.outScan.dto.request.OutScanReqDTO;
import com.zto.intl.cts.hk.tds.service.outScan.dto.response.OutOrderRespDTO;
import com.zto.intl.cts.hk.tds.service.outScan.dto.response.OutScanCheckRespDTO;
import com.zto.intl.cts.hk.tds.service.outScan.dto.response.OutScanQueryRespDTO;
import com.zto.intl.cts.hk.tds.service.outScan.dto.response.OutScanRespDTO;
import com.zto.intl.cts.hk.tds.service.task.trace.TraceTaskService;
import com.zto.intl.cts.hk.tds.service.task.trace.dto.request.TaskTraceAddReqDTO;
import com.zto.intl.cts.hk.tds.service.arriveScan.ArriveScanService;
import com.zto.intl.cts.hk.tds.dao.entity.ArriveScan;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 到货扫描服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DubboOutScanServiceImpl implements IDubboOutScanService {

    private final OmsService omsService;
    private final TraceTaskService traceTaskService;
    private final OutScanService outScanService;
    private final ActionMachineService actionMachineService;
    private final ArriveScanService arriveScanService;


    @Override
    public DubboOutScanRespDTO outScan(DubboOutScanReqDTO scanReqDTO) {
        log.info("出货扫描，请求参数：{}", JSON.toJSONString(scanReqDTO));
        // 获取oms信息
        List<OmsOrderQueryRespDTO> omsOrders = getOmsOrderInfo(scanReqDTO.getNoTypeEnum(), scanReqDTO.getNoList());
        Map<String, OutOrderRespDTO> omsOrderMap = convertToOutOrderReqDTOMap(scanReqDTO.getNoTypeEnum(), omsOrders);
        // 出货扫描数据
        OutScanReqDTO outScanDTO = new OutScanReqDTO();
        BeanUtil.copyProperties(scanReqDTO, outScanDTO);

        // 状态机校验
        List<ActionMachineValidDTO> machineReqDTOS = omsOrders.stream()
                .map(e -> new ActionMachineValidDTO(e.getBillStatus(), e.getBillCode()))
                .collect(Collectors.toList());
        DubboActionMachineRespDTO valid = actionMachineService.validBatch(ActionCodeEnum.C30.getCode(), machineReqDTOS);
        if(!valid.getSuccess()) {
            outScanDTO.setErrorInfo(valid.getErrorInfo());
        }

        // 执行出货扫描
        OutScanRespDTO outScanRespDTO = outScanService.outScan(outScanDTO, omsOrderMap);
        // 推送出货轨迹
        addOutScanTrace(outScanRespDTO, outScanDTO);

        DubboOutScanRespDTO dubboOutScanRespDTO = new DubboOutScanRespDTO();
        BeanUtil.copyProperties(outScanRespDTO, dubboOutScanRespDTO);

        log.info("出货扫描结果，{}", JSON.toJSONString(dubboOutScanRespDTO));
        return dubboOutScanRespDTO;
    }

    @Override
    public PageResult<DubboOutScanQueryRespDTO> page(PageQuery<DubboOutScanQueryReqDTO> pageQuery) {
        log.info("开始查询出货扫描结果，请求参数：{}", JSON.toJSONString(pageQuery));

        // 1. 转换请求参数
        PageQuery<OutScanQueryReqDTO> servicePageQuery = pageQuery.convert(dubboCondition -> {
            OutScanQueryReqDTO serviceCondition = new OutScanQueryReqDTO();
            BeanUtil.copyProperties(dubboCondition, serviceCondition);
            return serviceCondition;
        });

        // 2. 调用服务层查询
        PageResult<OutScanQueryRespDTO> serviceResult = outScanService.queryOutScanResult(servicePageQuery);

        // 3. 转换响应结果
        PageResult<DubboOutScanQueryRespDTO> dubboResult = serviceResult.convert(this::convertToDubboQueryRespDTO);
        // TODO 替换工具类
//        PageResult<DubboOutScanQueryRespDTO> dubboResult = PageMapUtil.map(serviceResult, DubboOutScanQueryRespDTO.class);

        log.info("查询出货扫描结果完成，总记录数：{}", serviceResult.getTotal());
        return dubboResult;
    }

    @Override
    public DubboOutScanCheckRespDTO checkOrder(DubboOutScanCheckReqDTO checkReqDTO) {
        log.info("开始校验订单，请求参数：{}", JSON.toJSONString(checkReqDTO));

        // 1. 批量查询订单信息
        List<OmsOrderQueryRespDTO> omsOrders = getOmsOrderInfo(checkReqDTO.getNoTypeEnum(), checkReqDTO.getNoList());
        Map<String, OutOrderRespDTO> orderInfoMap = convertToOutOrderReqDTOMap(checkReqDTO.getNoTypeEnum(), omsOrders);

        // 2. 状态机校验（如果是单个扫描）
        ErrorInfo errorInfo = null;
        if (Objects.equals(ScanTypeEnum.SINGLE, checkReqDTO.getScanType())) {
            List<ActionMachineValidDTO> machineReqDTOS = omsOrders.stream()
                    .map(e -> new ActionMachineValidDTO(e.getBillStatus(), e.getBillCode()))
                    .collect(Collectors.toList());
            DubboActionMachineRespDTO valid = actionMachineService.validBatch(ActionCodeEnum.C30.getCode(), machineReqDTOS);
            if (!valid.getSuccess()) {
                errorInfo = valid.getErrorInfo();
            }
        }

        // 3. 查询到货扫描记录（如果是单个扫描）
        Map<String, ArriveScan> arriveScanMap = null;
        if (Objects.equals(ScanTypeEnum.SINGLE, checkReqDTO.getScanType())) {
            arriveScanMap = arriveScanService.getExistingScanMap(checkReqDTO.getNoList(), checkReqDTO.getNoTypeEnum());
        }

        // 4. 调用service层校验逻辑
        OutScanCheckRespDTO serviceResult = outScanService.checkBillCodes(
                checkReqDTO.getNoList(),
                checkReqDTO.getNoTypeEnum(),
                orderInfoMap,
                checkReqDTO.getScanType(),
                checkReqDTO.getSiteCode(),
                errorInfo,
                arriveScanMap
        );

        // 5. 转换结果
        DubboOutScanCheckRespDTO result = convertToScanCheckRespDTO(serviceResult);

        log.info("订单校验完成，单号数量：{}，需要二次确认：{}，扫描类型：{}",
                checkReqDTO.getNoList().size(), result.getNeedConfirm(), checkReqDTO.getScanType());
        return result;
    }

    /**
     * 转换OutScanQueryRespDTO为DubboOutScanQueryRespDTO
     */
    private DubboOutScanQueryRespDTO convertToDubboQueryRespDTO(OutScanQueryRespDTO serviceResp) {
        DubboOutScanQueryRespDTO dubboResp = new DubboOutScanQueryRespDTO();
        BeanUtil.copyProperties(serviceResp, dubboResp);
        return dubboResp;
    }

    /**
     * 出货轨迹
     */
    private void addOutScanTrace(OutScanRespDTO outScanRespDTO, OutScanReqDTO outScanDTO) {
        List<OutOrderRespDTO> successBillList = outScanRespDTO.getSuccessOrderList();
        List<TaskTraceAddReqDTO> taskList = new ArrayList<>();
        for(OutOrderRespDTO outOrderReqDTO : successBillList) {
            TaskTraceAddReqDTO taskTraceAddReqDTO = new TaskTraceAddReqDTO();
            taskTraceAddReqDTO.setBillCode(outOrderReqDTO.getBillCode());
            taskTraceAddReqDTO.setCustomerOrderNo(outOrderReqDTO.getCustomerOrderNo());
            taskTraceAddReqDTO.setActionCode(TraceEnum.C30.getActionCode());
            taskTraceAddReqDTO.setActionName(TraceEnum.C30.getActionName());
            taskTraceAddReqDTO.setTraceAction(TraceEnum.C30.getTraceAction());
            taskTraceAddReqDTO.setSiteCode(outScanDTO.getSiteCode());
            taskTraceAddReqDTO.setSiteName(outScanDTO.getSiteName());
            taskTraceAddReqDTO.setOperator(outScanDTO.getOperator());
            taskTraceAddReqDTO.setOperateTime(new Date());

            taskList.add(taskTraceAddReqDTO);
        }

        traceTaskService.addTaskBatch(taskList);
    }

    /**
     * 获取oms分站运单信息
     */
    private List<OmsOrderQueryRespDTO> getOmsOrderInfo(ScanNoTypeEnum noTypeEnum, List<String> noList) {
        List<OmsOrderQueryRespDTO> omsDatas;
        if(Objects.equals(ScanNoTypeEnum.CUSTOMER_ORDER_NO, noTypeEnum)) {
            omsDatas = omsService.listByCustomerOrdrNos(noList);
        } else {
            omsDatas = omsService.listByBillCodes(noList);
        }

        return omsDatas;
    }

    /**
     * List<OmsOrderQueryRespDTO> 转Map<String, OutOrderRespDTO>
     */
    private Map<String, OutOrderRespDTO> convertToOutOrderReqDTOMap(ScanNoTypeEnum noTypeEnum, List<OmsOrderQueryRespDTO> omsData) {
        return omsData.stream()
                .collect(Collectors.toMap(
                        Objects.equals(ScanNoTypeEnum.CUSTOMER_ORDER_NO, noTypeEnum) ? OmsOrderQueryRespDTO::getCustomerOrderNo :OmsOrderQueryRespDTO::getBillCode ,
                        this::convertToOutOrderReqDTO,
                        (existing, replacement) -> existing));
    }

    /**
     * 将OmsOrderQueryRespDTO转换为OutOrderRespDTO
     */
    private OutOrderRespDTO convertToOutOrderReqDTO(OmsOrderQueryRespDTO omsOrder) {
        OutOrderRespDTO outOrder = new OutOrderRespDTO();
        BeanUtil.copyProperties(omsOrder, outOrder);
        return outOrder;
    }

    /**
     * 转换OutScanCheckRespDTO为DubboOutScanCheckRespDTO
     */
    private DubboOutScanCheckRespDTO convertToScanCheckRespDTO(OutScanCheckRespDTO serviceResult) {
        DubboOutScanCheckRespDTO result = new DubboOutScanCheckRespDTO();
        result.setNeedConfirm(serviceResult.getNeedConfirm());

        if (CollectionUtils.isNotEmpty(serviceResult.getErrors())) {
            List<ErrorInfo> errors = serviceResult.getErrors().stream()
                    .map(serviceError -> new ErrorInfo(
                            serviceError.getBillNos(),
                            serviceError.getErrorMsg()
                    )).collect(Collectors.toList());
            result.setErrors(errors);
        }
        return result;
    }
}
