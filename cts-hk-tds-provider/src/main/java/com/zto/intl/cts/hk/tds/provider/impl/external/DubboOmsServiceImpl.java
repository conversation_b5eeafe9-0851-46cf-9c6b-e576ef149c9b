package com.zto.intl.cts.hk.tds.provider.impl.external;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSONObject;
import com.zto.intl.cts.hk.commons.util.Asserts;
import com.zto.intl.cts.hk.core.domain.external.BaseResponse;
import com.zto.intl.cts.hk.core.exception.BizException;
import com.zto.intl.cts.hk.tds.dao.entity.ArriveScan;
import com.zto.intl.cts.hk.tds.enums.TraceEnum;
import com.zto.intl.cts.hk.tds.error.ErrorEnum;
import com.zto.intl.cts.hk.tds.facade.api.external.IDubboOmsService;
import com.zto.intl.cts.hk.tds.facade.api.external.dto.request.DubboOrderPayStatusDTO;
import com.zto.intl.cts.hk.tds.service.arriveScan.ArriveScanService;
import com.zto.intl.cts.hk.tds.service.task.trace.TraceTaskService;
import com.zto.intl.cts.hk.tds.service.task.trace.dto.request.TaskTraceAddReqDTO;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * oms交互dubbo服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DubboOmsServiceImpl implements IDubboOmsService {

    private final ArriveScanService arriveScanService;
    private final TraceTaskService traceTaskService;

    @Override
    public BaseResponse<String> receiveOrderPayStatus(DubboOrderPayStatusDTO request) {
        log.info("oms支付状态回传入参：{}", JSONObject.toJSONString(request));

        try {
            // 校验
            check(request);

            // 是否完成到货扫描
            List<ArriveScan> arriveScans = arriveScanService.queryArriveScan(request.getBillCode());
            Asserts.isTrue(CollectionUtils.isNotEmpty(arriveScans), ErrorEnum.OmsError.ARRIVE_SCAN_RECORD_NULL);

            // 更新支付状态
            arriveScanService.updatePayStatusByBillCode(
                    request.getBillCode(),
                    request.getPayStatus()
            );

            // 新增收款完成轨迹
            addPayedTrace(arriveScans.get(0));
        } catch (BizException e) {
            log.error("更新支付状态异常，运单号：{} 错误信息：", request.getBillCode(), e);
            return BaseResponse.fail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("更新支付状态异常，运单号：{} 错误信息：", request.getBillCode(), e);
            return BaseResponse.fail(ErrorEnum.OmsError.PAY_STATUS_UPDATE_ERROR);
        }
        return BaseResponse.success("更新成功");
    }

    /**
     * 收款完成轨迹
     */
    private void addPayedTrace(ArriveScan arriveScan) {
        TaskTraceAddReqDTO taskTraceAddReqDTO = new TaskTraceAddReqDTO();
        taskTraceAddReqDTO.setBillCode(arriveScan.getBillCode());
        taskTraceAddReqDTO.setCustomerOrderNo(arriveScan.getCustomerOrderNo());
        taskTraceAddReqDTO.setActionCode(TraceEnum.WS.getActionCode());
        taskTraceAddReqDTO.setSiteCode(arriveScan.getSiteCode());
        taskTraceAddReqDTO.setSiteName(arriveScan.getSiteName());
        taskTraceAddReqDTO.setOperator(arriveScan.getOperator());
        taskTraceAddReqDTO.setOperateTime(new Date());
        traceTaskService.addTask(taskTraceAddReqDTO);
    }

    /**
     * 校验
     */
    private void check(DubboOrderPayStatusDTO request) {
        Asserts.isTrue(StringUtils.isNotBlank(request.getBillCode()), ErrorEnum.OmsError.BILL_NO_NULL);
        Asserts.isTrue(request.getPayStatus() != null, ErrorEnum.OmsError.PAY_STATUS_NULL);
    }
}
