package com.zto.intl.cts.hk.tds.provider.impl.web.actionScan;

import com.alibaba.dubbo.config.annotation.Service;
import com.zto.intl.cts.hk.commons.util.BeanUtil;
import com.zto.intl.cts.hk.commons.util.PageMapUtil;
import com.zto.intl.cts.hk.core.domain.web.PageQuery;
import com.zto.intl.cts.hk.core.domain.web.PageResult;
import com.zto.intl.cts.hk.tds.adapter.oms.OmsService;
import com.zto.intl.cts.hk.tds.facade.api.web.actionScan.DubboActionScanService;
import com.zto.intl.cts.hk.tds.facade.api.web.actionScan.dto.request.DubboActionScanPageReqDTO;
import com.zto.intl.cts.hk.tds.facade.api.web.actionScan.dto.request.DubboActionScanReqDTO;
import com.zto.intl.cts.hk.tds.facade.api.web.actionScan.dto.response.DubboActionScanPageRespDTO;
import com.zto.intl.cts.hk.tds.facade.api.web.actionScan.dto.response.DubboActionScanRespDTO;
import com.zto.intl.cts.hk.tds.service.actionScan.ActionScanService;
import com.zto.intl.cts.hk.tds.service.actionScan.dto.request.ActionScanPageReqDTO;
import com.zto.intl.cts.hk.tds.service.actionScan.dto.request.ActionScanReqDTO;
import com.zto.intl.cts.hk.tds.service.actionScan.dto.response.ActionScanPageRespDTO;
import com.zto.intl.cts.hk.tds.service.actionScan.dto.response.ActionScanRespDTO;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Description 状态扫描服务
 * @Date 2025/7/29
 * @Version 1.0
 */
@Service
public class DubboActionScanServiceImpl implements DubboActionScanService {

    @Autowired
    private ActionScanService actionScanService;


    @Override
    public void scan(DubboActionScanReqDTO reqDTO){

         // 执行扫描 ：保存记录 并返回异常信息
        ActionScanReqDTO actionScanReqDTO = BeanUtil.copyProperties(reqDTO, new ActionScanReqDTO());

        actionScanService.actionScan(actionScanReqDTO);

    }

    @Override
    public DubboActionScanRespDTO scanBatch(DubboActionScanReqDTO reqDTO){

        ActionScanReqDTO dto = BeanUtil.copyProperties(reqDTO, new ActionScanReqDTO());
        ActionScanRespDTO actionScanRespDTO = actionScanService.actionScanBatch(dto);

        return BeanUtil.copyProperties(actionScanRespDTO, new DubboActionScanRespDTO());

    }

    @Override
    public PageResult<DubboActionScanPageRespDTO> page(PageQuery<DubboActionScanPageReqDTO> dto){

        PageQuery<ActionScanPageReqDTO> map = PageMapUtil.map(dto, ActionScanPageReqDTO.class);
        PageResult<ActionScanPageRespDTO> page = actionScanService.page(map);

        return PageMapUtil.map(page, DubboActionScanPageRespDTO.class);
    }


}
