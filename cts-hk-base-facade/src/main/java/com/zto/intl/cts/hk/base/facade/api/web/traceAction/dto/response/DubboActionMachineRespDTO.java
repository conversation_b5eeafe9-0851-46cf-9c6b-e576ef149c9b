package com.zto.intl.cts.hk.base.facade.api.web.traceAction.dto.response;

import com.zto.intl.cts.hk.core.domain.web.ErrorInfo;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 状态机响应
 * @Date 2025/8/2
 * @Version 1.0
 */
public class DubboActionMachineRespDTO implements Serializable {

    private static final long serialVersionUID = 1636345289495202493L;

    /**
     * 是否成功
     */
    private Boolean success;
    /**
     * 单条订单校验，失败信息
     */
    private String errorMsg;
    /**
     * 批量订单校验，失败信息
     */
    private ErrorInfo errorInfo;

    public static DubboActionMachineRespDTO success(){
        DubboActionMachineRespDTO respDTO = new DubboActionMachineRespDTO();
        respDTO.success = true;
        return respDTO;
    }

    public static DubboActionMachineRespDTO fail(String errorMsg){
        DubboActionMachineRespDTO respDTO = new DubboActionMachineRespDTO();
        respDTO.success = false;
        respDTO.errorMsg = errorMsg;
        return respDTO;
    }

    public static DubboActionMachineRespDTO fail(String errorMsg, List<String> billNos){
        DubboActionMachineRespDTO respDTO = new DubboActionMachineRespDTO();
        respDTO.success = false;
        ErrorInfo errorInfo = new ErrorInfo(billNos, errorMsg);
        respDTO.setErrorInfo(errorInfo);
        return respDTO;
    }

    private DubboActionMachineRespDTO(){
        this.success = true;
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }
    public ErrorInfo getErrorInfo() {
        return errorInfo;
    }

    public void setErrorInfo(ErrorInfo errorInfo) {
        this.errorInfo = errorInfo;
    }



}
