package com.zto.intl.cts.hk.base.facade.api.web.traceAction;

import com.zto.intl.cts.hk.base.facade.api.web.traceAction.dto.response.DubboTraceActionRespDTO;

import java.util.List;

public interface DubboTraceActionService {

    /**
     *
     * <AUTHOR>
     * @Return 根据状态码精确查询
     * @Date 2025/8/2
     */
    DubboTraceActionRespDTO getByActionCode(String actionCode);

    /**
     *
     * <AUTHOR>
     * @Return 根据关键字模糊查询
     * @Date 2025/8/2
     */
    List<DubboTraceActionRespDTO> likeByCodeOrName(String keyword);
}
