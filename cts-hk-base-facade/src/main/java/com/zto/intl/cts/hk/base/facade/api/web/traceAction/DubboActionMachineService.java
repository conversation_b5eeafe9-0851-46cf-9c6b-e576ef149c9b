package com.zto.intl.cts.hk.base.facade.api.web.traceAction;

import com.zto.intl.cts.hk.base.facade.api.web.traceAction.dto.request.DubboActionMachineReqDTO;
import com.zto.intl.cts.hk.base.facade.api.web.traceAction.dto.response.DubboActionMachineRespDTO;

import java.util.List;

public interface DubboActionMachineService {

    DubboActionMachineRespDTO validSingle(String actionCode, String orderStatus);

    DubboActionMachineRespDTO validBatch(String actionCode, List<DubboActionMachineReqDTO> orders);
}
