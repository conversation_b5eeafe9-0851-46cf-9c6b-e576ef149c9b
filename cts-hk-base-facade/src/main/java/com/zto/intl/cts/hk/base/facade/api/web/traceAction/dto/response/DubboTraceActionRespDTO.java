package com.zto.intl.cts.hk.base.facade.api.web.traceAction.dto.response;

import java.io.Serializable;

public class DubboTraceActionRespDTO implements Serializable {

    private static final long serialVersionUID = -2365529853963425638L;
    /**
     * 操作状态码
     */
    private String actionCode;

    /**
     * 操作状态描述
     */
    private String actionName;

    /**
     * 轨迹状态码
     */
    private String traceActionCodeCode;

    /**
     * 是否推送轨迹系统，0 否 1 是
     */
    private Boolean needPush;

    /**
     * 轨迹描述
     */
    private String message;

    /**
     * 订单状态白名单
     */
    private String whiteList;

    /**
     * 订单状态黑名单
     */
    private String blackList;

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getActionCode() {
        return actionCode;
    }

    public void setActionCode(String actionCode) {
        this.actionCode = actionCode;
    }

    public String getActionName() {
        return actionName;
    }

    public void setActionName(String actionName) {
        this.actionName = actionName;
    }

    public String getTraceActionCode() {
        return traceActionCodeCode;
    }

    public void setTraceAction(String traceActionCodeCode) {
        this.traceActionCodeCode = traceActionCodeCode;
    }

    public Boolean getNeedPush() {
        return needPush;
    }

    public void setNeedPush(Boolean needPush) {
        this.needPush = needPush;
    }

    public String getWhiteList() {
        return whiteList;
    }

    public void setWhiteList(String whiteList) {
        this.whiteList = whiteList;
    }

    public String getBlackList() {
        return blackList;
    }

    public void setBlackList(String blackList) {
        this.blackList = blackList;
    }


}
