package com.zto.intl.cts.hk.core.enums;

public enum ScanTypeEnum {

    SINGLE(1, "单件"),
    BATCH(2, "批量"),
    ;

    private Integer code;
    private String name;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    ScanTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }
}
