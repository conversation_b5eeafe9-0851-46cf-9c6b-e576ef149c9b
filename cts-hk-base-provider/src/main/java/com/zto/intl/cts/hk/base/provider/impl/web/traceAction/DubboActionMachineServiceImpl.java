package com.zto.intl.cts.hk.base.provider.impl.web.traceAction;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.alibaba.dubbo.config.annotation.Service;
import com.zto.intl.common.enums.oversea.HkBillStatusEnum;
import com.zto.intl.cts.hk.base.facade.api.web.traceAction.DubboActionMachineService;
import com.zto.intl.cts.hk.base.facade.api.web.traceAction.DubboTraceActionService;
import com.zto.intl.cts.hk.base.facade.api.web.traceAction.dto.request.DubboActionMachineReqDTO;
import com.zto.intl.cts.hk.base.facade.api.web.traceAction.dto.response.DubboActionMachineRespDTO;
import com.zto.intl.cts.hk.base.facade.api.web.traceAction.dto.response.DubboTraceActionRespDTO;
import com.zto.intl.cts.hk.commons.constant.SymbolConstant;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 状态机器
 * @Date 2025/8/2
 * @Version 1.0
 */
// TODO 这个应该放在external,并用BaseResponse封装
@Service
public class DubboActionMachineServiceImpl implements DubboActionMachineService {

    @Resource
    private DubboTraceActionService dubboTraceActionService;

    @Override
    public DubboActionMachineRespDTO validSingle(String actionCode, String orderStatus){

        String errorMsg = buildErrorMsg(actionCode);
        if(StringUtil.isBlank(errorMsg)){
            return DubboActionMachineRespDTO.success();
        }

        return doValid(actionCode, orderStatus) ? DubboActionMachineRespDTO.success() : DubboActionMachineRespDTO.fail(errorMsg);
    }

    @Override
    public DubboActionMachineRespDTO validBatch(String actionCode, List<DubboActionMachineReqDTO> orders){

        String errorMsg = buildErrorMsg(actionCode);
        if(StringUtil.isBlank(errorMsg)){
            return DubboActionMachineRespDTO.success();
        }

        List<String> errorBillNos = new ArrayList<>();
        orders.forEach(e ->{

            if(doValid(actionCode, e.getOrderStatus())){
                errorBillNos.add(e.getBizCode());
            }
        });

        return DubboActionMachineRespDTO.fail(errorMsg, errorBillNos);
    }


    /**
     * 错误描述
     * <AUTHOR>
     * @Date 2025/8/2
     */
    private String buildErrorMsg(String actionCode) {

        // 获取状态
        DubboTraceActionRespDTO action = dubboTraceActionService.getByActionCode(actionCode);
        if(null == action){
            return "非法的状态代码";
        }

        if (StringUtil.isNotBlank(action.getWhiteList())) {
            return action.getActionName() + "仅允许" + codeToName(action.getWhiteList());
        }

        if (StringUtil.isNotBlank(action.getBlackList())) {
            return action.getActionName() + "不允许" + codeToName(action.getBlackList());
        }

        return null;
    }

    private String codeToName(String codeStr){
        String[] codes = codeStr.split(SymbolConstant.COMMA);
        List<String> names = Arrays.stream(codes).map(e -> HkBillStatusEnum.getName(e).name()).collect(Collectors.toList());
        return String.join(SymbolConstant.COMMA, names);

    }


    /**
     * 校验
     * <AUTHOR>
     * @Date 2025/8/2
     */
    private Boolean doValid(String actionCode, String orderStatus){

        // 获取状态
        DubboTraceActionRespDTO action = dubboTraceActionService.getByActionCode(actionCode);

        // 订单状态代码转为枚举
        if(StringUtil.isNotBlank(action.getWhiteList())){
            return Arrays.asList(action.getWhiteList().split(SymbolConstant.COMMA)).contains(orderStatus);
        }

        if(StringUtil.isNotBlank(action.getBlackList())){
            return ! Arrays.asList(action.getBlackList().split(SymbolConstant.COMMA)).contains(orderStatus);
        }

        return null;
    }


}
