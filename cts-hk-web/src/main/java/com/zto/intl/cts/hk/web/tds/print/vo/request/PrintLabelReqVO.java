package com.zto.intl.cts.hk.web.tds.print.vo.request;

import com.zto.intl.cts.hk.core.enums.ScanNoTypeEnum;
import com.zto.intl.cts.hk.tds.facade.enums.PrintTypeEnum;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @date 20225/7/24
 */
@Data
public class PrintLabelReqVO {

    /** 单号类型 运单号: BILL_CODE, 客户单号：CUSTOMER_ORDER_NO **/
    @NotNull(message = "单号类型不能为空")
    private ScanNoTypeEnum noTypeEnum;

    /** 单号 **/
    @NotNull(message = "单号不能为空")
    @Size(max = 100, message = "选中导出不能超过100条")
    private List<String> noList;

    /** 打印类型 **/
    private PrintTypeEnum printTypeEnum;
}
