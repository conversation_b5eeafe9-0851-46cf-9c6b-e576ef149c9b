package com.zto.intl.cts.hk.web.tds.arriveScan.vo.request;

import com.zto.intl.cts.hk.core.enums.ScanNoTypeEnum;
import com.zto.intl.cts.hk.core.enums.ScanTypeEnum;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 到货扫描校验请求VO
 *
 * <AUTHOR>
 * @date 2025/8/1
 */
@Data
public class ArriveScanCheckReqVO {
    /**
     * 单号类型
     * 运单号: BILL_CODE,
     * 客户单号：CUSTOMER_ORDER_NO
     */
    @NotNull(message = "单号类型不能为空")
    private ScanNoTypeEnum noTypeEnum;

    /** 单号列表（支持单个和批量） **/
    @NotNull(message = "单号不能为空")
    private List<String> noList;

    /** 称重重量（可选，用于重量差异校验） **/
    private BigDecimal weight;

    /** 扫描类型（可选，用于单个扫描时的额外业务规则校验） **/
    private ScanTypeEnum scanType;
}
