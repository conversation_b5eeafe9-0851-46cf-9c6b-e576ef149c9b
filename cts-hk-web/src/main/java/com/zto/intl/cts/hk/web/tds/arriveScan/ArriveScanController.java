package com.zto.intl.cts.hk.web.tds.arriveScan;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.zto.intl.cts.hk.commons.constant.SysConstant;
import com.zto.intl.cts.hk.commons.error.SysErrorEnum;
import com.zto.intl.cts.hk.commons.util.BeanUtil;
import com.zto.intl.cts.hk.commons.util.PageMapUtil;
import com.zto.intl.cts.hk.core.domain.web.ErrorInfo;
import com.zto.intl.cts.hk.tds.facade.api.web.arriveScan.IDubboArriveScanService;
import com.zto.intl.cts.hk.tds.facade.api.web.arriveScan.dto.request.DubboArriveScanQueryReqDTO;
import com.zto.intl.cts.hk.tds.facade.api.web.arriveScan.dto.request.DubboArriveScanReqDTO;
import com.zto.intl.cts.hk.tds.facade.api.web.arriveScan.dto.request.DubboArriveScanCheckReqDTO;
import com.zto.intl.cts.hk.tds.facade.api.web.arriveScan.dto.request.DubboScanWeightReqDTO;
import com.zto.intl.cts.hk.tds.facade.api.web.arriveScan.dto.response.DubboArriveScanQueryRespDTO;
import com.zto.intl.cts.hk.tds.facade.api.web.arriveScan.dto.response.DubboArriveScanRespDTO;
import com.zto.intl.cts.hk.core.domain.web.PageQuery;
import com.zto.intl.cts.hk.core.domain.web.PageResult;
import com.zto.intl.cts.hk.web.base.WebResponse;
import com.zto.intl.cts.hk.web.tds.arriveScan.vo.export.ExportRequestVO;
import com.zto.intl.cts.hk.web.tds.arriveScan.vo.request.*;
import com.zto.intl.cts.hk.web.tds.arriveScan.vo.response.ArriveScanQueryRespVO;
import com.zto.intl.cts.hk.web.tds.arriveScan.vo.response.ArriveScanRespVO;
import com.zto.intl.cts.hk.web.tds.arriveScan.vo.response.ArriveScanCheckRespVO;
import com.zto.intl.cts.hk.web.tds.arriveScan.vo.export.ArriveScanExportVO;
import com.zto.intl.cts.hk.commons.util.EasyExcelUtils;
import com.zto.intl.cts.hk.tds.facade.api.web.arriveScan.dto.response.DubboArriveScanCheckRespDTO;
import com.zto.titans.web.filter.User;
import com.zto.zsmp.annotation.ZsmpApiOperation;
import com.zto.zsmp.annotation.ZsmpService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;

/**
 * 到货扫描
 * <AUTHOR>
 * @date 2025/7/29
 */
@Slf4j
@RequestMapping("arriveScan")
@RestController
@ZsmpService(businessDomain = SysConstant.ZSMP_DOMAIN, name = "到货扫描", group = "到货扫描")
@RequiredArgsConstructor
public class ArriveScanController {
    // todo 测试站点，后面需要取用户里面的
    private static final String TEST_STIE_CODE = "OPS";
    private static final String TEST_STIE_NAME = "OPS";

    @Reference
    private IDubboArriveScanService arriveScanService;

    @PostMapping("/scan")
    //@PreAuthorize("@ctsPermissionService.hasAnyPermission('arrive_scan')")
    @ZsmpApiOperation(description = "到货扫描", name = "到货扫描")
    public WebResponse<ArriveScanRespVO> scan(@Valid @RequestBody ArriveScanReqVO arriveScanReqVO) {
       log.info("到货扫描入参：{}", JSON.toJSONString(arriveScanReqVO));
        User user = (User) SecurityContextHolder.getContext().getAuthentication();

        DubboArriveScanReqDTO arriveScanReqDTO = new DubboArriveScanReqDTO();
        BeanUtil.copyProperties(arriveScanReqVO, arriveScanReqDTO);
        if(user != null) {
            arriveScanReqDTO.setOperator(user.getName());
        }

        // todo 站点信息后续取user里面的
        arriveScanReqDTO.setSiteCode(TEST_STIE_CODE);
        arriveScanReqDTO.setSiteName(TEST_STIE_NAME);
        DubboArriveScanRespDTO dubboArriveScanRespDTO = arriveScanService.arriveScan(arriveScanReqDTO);
        ArriveScanRespVO arriveScanRespVO = new ArriveScanRespVO();
        BeanUtil.copyProperties(dubboArriveScanRespDTO, arriveScanRespVO);

        log.info("到货扫描响应：{}", JSON.toJSONString(arriveScanRespVO));
        return new WebResponse<>(arriveScanRespVO);
    }

    @PostMapping("/check")
    //@PreAuthorize("@ctsPermissionService.hasAnyPermission('arrive_scan')")
    @ZsmpApiOperation(description = "校验订单（支持批量和重量差异校验）", name = "校验订单")
    public WebResponse<ArriveScanCheckRespVO> check(@Valid @RequestBody ArriveScanCheckReqVO checkReqVO){
        log.info("订单校验入参：{}", JSON.toJSONString(checkReqVO));

        // 转换请求参数
        DubboArriveScanCheckReqDTO dubboScanCheckReqDTO = new DubboArriveScanCheckReqDTO();
        BeanUtil.copyProperties(checkReqVO, dubboScanCheckReqDTO);
        // todo 站点信息后续取user里面的
        dubboScanCheckReqDTO.setSiteCode(TEST_STIE_CODE);
        // 调用校验服务
        DubboArriveScanCheckRespDTO dubboResult = arriveScanService.checkOrder(dubboScanCheckReqDTO);

        // 转换响应结果
        ArriveScanCheckRespVO result = convertToCheckRespVO(dubboResult);

        log.info("订单校验完成，单号数量：{}，需要二次确认：{}，错误数量：{}",
                checkReqVO.getNoList().size(),
                result.getNeedConfirm(),
                result.getErrors() != null ? result.getErrors().size() : 0);

        return new WebResponse<>(result);
    }

    @PostMapping("/page")
    //@PreAuthorize("@ctsPermissionService.hasAnyPermission('arrive_scan')")
    @ZsmpApiOperation(description = "查询", name = "查询")
    public WebResponse<PageResult<ArriveScanQueryRespVO>> page(@RequestBody PageQuery<ArriveScanQueryReqVO> pageRequest){
        log.info("到货扫描查询入参：{}", JSON.toJSONString(pageRequest));

        // 1. 转换请求参数
        PageQuery<DubboArriveScanQueryReqDTO> dubboPageQuery = PageMapUtil.map(pageRequest, DubboArriveScanQueryReqDTO.class);

        // 2. 调用查询服务
        PageResult<DubboArriveScanQueryRespDTO> dubboResult = arriveScanService.page(dubboPageQuery);

        // 3. 转换响应结果
        PageResult<ArriveScanQueryRespVO> result = PageMapUtil.map(dubboResult, ArriveScanQueryRespVO.class);

        log.info("到货扫描查询完成，总记录数：{}", dubboResult.getTotal());
        return new WebResponse<>(result);
    }

    @PostMapping("/export")
    //@PreAuthorize("@ctsPermissionService.hasAnyPermission('arrive_scan')")
    @ZsmpApiOperation(description = "导出", name = "导出")
    public void export(@Valid @RequestBody ExportRequestVO<ArriveScanExportReqVO> exportReqVO, HttpServletResponse response) throws IOException {
        log.info("到货扫描导出入参：{}", JSON.toJSONString(exportReqVO));
        // 1. 直接构建Dubbo查询条件
        DubboArriveScanQueryReqDTO dubboQueryCondition = convertExportReqToDubboQuery(exportReqVO.getCondition());

        // 2. 构建初始分页查询参数，使用通配符类型
        PageQuery<?> initialPageQuery = PageQuery.of(dubboQueryCondition);

        // 3. 使用EasyExcelUtils导出Excel
        EasyExcelUtils.exportSingleSheet(
                response,
                this::queryDataForExportWrapper,
                initialPageQuery,
                ArriveScanExportVO.class,
                exportReqVO.getIncludeColumnFields()
        );

        log.info("到货扫描导出完成");
    }

    @PostMapping("/weight")
    //@PreAuthorize("@ctsPermissionService.hasAnyPermission('arrive_scan')")
    @ZsmpApiOperation(description = "更新称重", name = "更新称重")
    public WebResponse<String> weight(@Valid @RequestBody ArriveScanWeightReqVO arriveScanWeightReqVO){
        log.info("重量更新入参：{}", JSON.toJSONString(arriveScanWeightReqVO));
        User user = (User) SecurityContextHolder.getContext().getAuthentication();

        // 转换请求参数
        DubboScanWeightReqDTO dubboScanWeightReqDTO = new DubboScanWeightReqDTO();
        BeanUtil.copyProperties(arriveScanWeightReqVO, dubboScanWeightReqDTO);
        if(user != null) {
            dubboScanWeightReqDTO.setOperator(user.getName());
        }

        // 调用重量更新服务
        arriveScanService.updateWeightAndSize(dubboScanWeightReqDTO);

        log.info("重量更新成功，单号数量：{}", arriveScanWeightReqVO.getNoList().size());
        return new WebResponse<>("重量更新成功");
    }

    /**
     * 将ArriveScanExportReqVO转换为DubboArriveScanQueryReqDTO
     */
    private DubboArriveScanQueryReqDTO convertExportReqToDubboQuery(ArriveScanExportReqVO exportReqVO) {
        DubboArriveScanQueryReqDTO dubboQueryReq = new DubboArriveScanQueryReqDTO();
        BeanUtil.copyProperties(exportReqVO, dubboQueryReq);
        return dubboQueryReq;
    }

    /**
     * 直接查询Dubbo服务的方法（用于导出）
     */
    private PageResult<ArriveScanExportVO> queryDataForExport(PageQuery<DubboArriveScanQueryReqDTO> pageQuery) {
        // 1. 直接调用Dubbo查询服务
        PageResult<DubboArriveScanQueryRespDTO> dubboResult = arriveScanService.page(pageQuery);

        // 2. 转换为导出格式
        List<ArriveScanExportVO> exportList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dubboResult.getData())) {
            for (int i = 0; i < dubboResult.getData().size(); i++) {
                DubboArriveScanQueryRespDTO dubboResp = dubboResult.getData().get(i);
                ArriveScanExportVO exportVO = convertToExportVO(dubboResp);
                exportList.add(exportVO);
            }
        }

        // 3. 构建返回结果
        PageResult<ArriveScanExportVO> exportResult = new PageResult<>();
        exportResult.setData(exportList);
        exportResult.setTotal(dubboResult.getTotal());
        exportResult.setPageNum(dubboResult.getPageNum());
        exportResult.setPageSize(dubboResult.getPageSize());
        exportResult.setPages(dubboResult.getPages());

        return exportResult;
    }

    /**
     * 将DubboArriveScanQueryRespDTO转换为ArriveScanExportVO
     */
    private ArriveScanExportVO convertToExportVO(DubboArriveScanQueryRespDTO dubboResp) {
        ArriveScanExportVO exportVO = new ArriveScanExportVO();
        BeanUtil.copyProperties(dubboResp, exportVO);
        return exportVO;
    }

    /**
     * 包装方法，用于消除unchecked cast警告
     * 这个方法专门处理EasyExcelUtils的类型转换问题
     */
    @SuppressWarnings("unchecked")
    private PageResult<ArriveScanExportVO> queryDataForExportWrapper(PageQuery<?> pageQuery) {
        PageQuery<DubboArriveScanQueryReqDTO> typedPageQuery = (PageQuery<DubboArriveScanQueryReqDTO>) pageQuery;
        return queryDataForExport(typedPageQuery);
    }

    /**
     * 转换校验响应结果
     */
    private ArriveScanCheckRespVO convertToCheckRespVO(DubboArriveScanCheckRespDTO dubboResult) {
        ArriveScanCheckRespVO result = new ArriveScanCheckRespVO();
        result.setNeedConfirm(dubboResult.getNeedConfirm());

        if (CollectionUtils.isNotEmpty(dubboResult.getErrors())) {
            List<ErrorInfo> errors = dubboResult.getErrors().stream()
                    .map(dubboError -> {
                        ErrorInfo errorVO = new ErrorInfo();
                        errorVO.setErrorMsg(dubboError.getErrorMsg());
                        errorVO.setBillNos(dubboError.getBillNos());
                        return errorVO;
                    })
                    .collect(java.util.stream.Collectors.toList());
            result.setErrors(errors);
        }

        return result;
    }
}
