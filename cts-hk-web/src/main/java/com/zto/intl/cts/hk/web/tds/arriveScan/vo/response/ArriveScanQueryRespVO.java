package com.zto.intl.cts.hk.web.tds.arriveScan.vo.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 到货扫描结果
 * <AUTHOR>
 * @date 2025/07/24
 */
@Data
public class ArriveScanQueryRespVO {
    /** id **/
    private Long id;

    /** 扫描时间 **/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date operateTime;

    /** 运单号 **/
    private String billCode;

    /** 客户单号 **/
    private String customerOrderNo;

    /** 重量kg **/
    private BigDecimal weight;

    /** 长CM **/
    private BigDecimal length;

    /** 宽CM **/
    private BigDecimal width;

    /** 高CM **/
    private BigDecimal height;

    /** 支付状态 **/
    private Byte payStatus;

    /** 支付状态描述 **/
    private String payStatusDesc;

    /** 收件人名称 **/
    private String consigneeName;

    /** 收件人电话 **/
    private String consigneeMobile;

    /** 收件人详细地址 **/
    private String consigneeAddress;

    /** 末端派送方式 0: 派送 1:自提 **/
    private Integer deliveryMethod;

    /** 末端派送方式描述 **/
    private String deliveryMethodDesc;

    /** 扫描结果 **/
    private Boolean isSuccess;

    /** 扫描结果描述 **/
    private String scanResultDesc;

    /** 扫描失败原因 **/
    private String failMessage;

    /** 揽收人 **/
    private String operator;

}
