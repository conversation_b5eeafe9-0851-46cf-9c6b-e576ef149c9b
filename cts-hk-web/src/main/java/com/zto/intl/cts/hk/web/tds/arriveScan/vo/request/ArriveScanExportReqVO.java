package com.zto.intl.cts.hk.web.tds.arriveScan.vo.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 20225/7/24
 */
@Data
public class ArriveScanExportReqVO {

    /** 开始扫描时间 **/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /** 结束扫描时间 **/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    /** 支付状态 **/
    private Byte payStatus;

    /** 扫描结果 1-成功 0-失败 **/
    private Boolean scanResult;

    /** 勾选导出 id集合 **/
    @Size(max = 10000, message = "选中导出不能超过10000条")
    private List<Long> ids;
}
