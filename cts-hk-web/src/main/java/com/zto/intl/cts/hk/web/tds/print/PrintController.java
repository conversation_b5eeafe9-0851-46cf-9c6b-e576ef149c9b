package com.zto.intl.cts.hk.web.tds.print;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.zto.intl.cts.hk.commons.constant.SysConstant;
import com.zto.intl.cts.hk.commons.util.BeanUtil;
import com.zto.intl.cts.hk.tds.facade.api.web.print.IDubboPrintService;
import com.zto.intl.cts.hk.tds.facade.api.web.print.dto.request.DubboPrintLabelReqDTO;
import com.zto.intl.cts.hk.tds.facade.api.web.print.dto.response.DubboPrintLabelRespDTO;
import com.zto.intl.cts.hk.web.base.WebResponse;
import com.zto.intl.cts.hk.web.tds.print.vo.request.PrintLabelReqVO;
import com.zto.intl.cts.hk.web.tds.print.vo.response.PrintLabelRespVO;
import com.zto.zsmp.annotation.ZsmpApiOperation;
import com.zto.zsmp.annotation.ZsmpService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 打印服务
 * <AUTHOR>
 * @date 2025/7/29
 */
@Slf4j
@RequestMapping("print")
@RestController
@ZsmpService(businessDomain = SysConstant.ZSMP_DOMAIN, name = "打印服务", group = "打印服务")
@RequiredArgsConstructor
public class PrintController {

    @Reference
    private IDubboPrintService printService;

    @PostMapping("/printLabel")
    @ZsmpApiOperation(description = "打印面单", name = "打印面单")
    public WebResponse<PrintLabelRespVO> printLabel(@Valid @RequestBody PrintLabelReqVO printLabelReqVO) {
        log.info("面单打印请求入参：{}", JSON.toJSONString(printLabelReqVO));

        // 1. 转换请求参数
        DubboPrintLabelReqDTO dubboReqDTO = new DubboPrintLabelReqDTO();
        BeanUtil.copyProperties(printLabelReqVO, dubboReqDTO);

        // 2. 调用Dubbo服务
        DubboPrintLabelRespDTO dubboRespDTO = printService.printLabel(dubboReqDTO);

        // 3. 转换响应结果
        PrintLabelRespVO respVO = new PrintLabelRespVO();
        BeanUtil.copyProperties(dubboRespDTO, respVO);

        log.info("面单打印成功，运单数量：{}，面单URL：{}",
                printLabelReqVO.getNoList().size(), respVO.getLabelUrl());

        return WebResponse.success(respVO);
    }
}
