package com.zto.intl.cts.hk.web.tds.actionScan.vo.request;

import com.zto.intl.cts.hk.core.enums.ScanNoTypeEnum;
import com.zto.intl.cts.hk.core.enums.ScanTypeEnum;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 状态扫描请求
 * @Date 2025/7/29
 * @Version 1.0
 */
@Getter
@Setter
public class ActionScanReqVO implements Serializable {
    private static final long serialVersionUID = 2352599016345775935L;

    /**
     * 单号类型
     */
    @NotNull(message = "扫描单号类型不能为空")
    private ScanNoTypeEnum noTypeEnum;

    @NotNull(message = "扫描方式不能为空")
    private ScanTypeEnum scanTypeEnum;

    /**
     * 单号批量
     */
    @Size(max = 1000, message = "批量不能超过1000条")
    private List<String> noList;
    /**
     * 状态代码
     */
    @NotBlank(message = "状态代码不能为空")
    private String actionCode;

}
