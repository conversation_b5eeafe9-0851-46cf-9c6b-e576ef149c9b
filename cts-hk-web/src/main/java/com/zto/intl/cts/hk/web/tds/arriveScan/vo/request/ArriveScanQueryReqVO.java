package com.zto.intl.cts.hk.web.tds.arriveScan.vo.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 20225/7/24
 */
@Data
public class ArriveScanQueryReqVO {

    /** 开始扫描时间 **/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /** 结束扫描时间 **/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    /** 支付状态，0：待支付，1：支付中，2：支付成功，3：支付失败，4：取消关闭，5：超时取消 **/
    private Byte payStatus;

    /** 扫描结果 1-成功 0-失败 **/
    private Boolean scanResult;
}
