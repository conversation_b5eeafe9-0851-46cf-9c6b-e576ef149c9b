package com.zto.intl.cts.hk.web.tds.outScan;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.zto.intl.cts.hk.commons.constant.SysConstant;
import com.zto.intl.cts.hk.commons.error.SysErrorEnum;
import com.zto.intl.cts.hk.commons.util.BeanUtil;
import com.zto.intl.cts.hk.commons.util.PageMapUtil;
import com.zto.intl.cts.hk.core.domain.web.ErrorInfo;
import com.zto.intl.cts.hk.core.domain.web.PageQuery;
import com.zto.intl.cts.hk.core.domain.web.PageResult;
import com.zto.intl.cts.hk.tds.facade.api.web.outScan.IDubboOutScanService;
import com.zto.intl.cts.hk.tds.facade.api.web.outScan.dto.request.DubboOutScanCheckReqDTO;
import com.zto.intl.cts.hk.tds.facade.api.web.outScan.dto.request.DubboOutScanQueryReqDTO;
import com.zto.intl.cts.hk.tds.facade.api.web.outScan.dto.request.DubboOutScanReqDTO;
import com.zto.intl.cts.hk.tds.facade.api.web.outScan.dto.response.DubboOutScanCheckRespDTO;
import com.zto.intl.cts.hk.tds.facade.api.web.outScan.dto.response.DubboOutScanQueryRespDTO;
import com.zto.intl.cts.hk.tds.facade.api.web.outScan.dto.response.DubboOutScanRespDTO;
import com.zto.intl.cts.hk.web.base.WebResponse;
import com.zto.intl.cts.hk.web.tds.arriveScan.ArriveScanController;
import com.zto.intl.cts.hk.web.tds.outScan.vo.request.OutScanCheckReqVO;
import com.zto.intl.cts.hk.web.tds.outScan.vo.request.OutScanQueryReqVO;
import com.zto.intl.cts.hk.web.tds.outScan.vo.request.OutScanReqVO;
import com.zto.intl.cts.hk.web.tds.outScan.vo.response.OutScanCheckRespVO;
import com.zto.intl.cts.hk.web.tds.outScan.vo.response.OutScanQueryRespVO;
import com.zto.intl.cts.hk.web.tds.outScan.vo.response.OutScanRespVO;
import com.zto.titans.web.filter.User;
import com.zto.zsmp.annotation.ZsmpApiOperation;
import com.zto.zsmp.annotation.ZsmpService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * 出货扫描
 * <AUTHOR>
 * @date 2025/7/29
 */
@Slf4j
@RequestMapping("outScan")
@RestController
@ZsmpService(businessDomain = SysConstant.ZSMP_DOMAIN, name = "出货扫描", group = "出货扫描")
@RequiredArgsConstructor
public class OutScanController {

    @Reference
    private IDubboOutScanService outScanService;

    @PostMapping("/scan")
    //@PreAuthorize("@ctsPermissionService.hasAnyPermission('out_scan')")
    @ZsmpApiOperation(description = "出货扫描", name = "出货扫描")
    public WebResponse<OutScanRespVO> scan(@Valid @RequestBody OutScanReqVO outScanReqVO) {
        log.info("出货扫描入参：{}", JSON.toJSONString(outScanReqVO));
        User user = (User) SecurityContextHolder.getContext().getAuthentication();

        DubboOutScanReqDTO outScanReqDTO = new DubboOutScanReqDTO();
        BeanUtil.copyProperties(outScanReqVO, outScanReqDTO);
        if(user != null) {
            outScanReqDTO.setOperator(user.getName());
        }
        // todo 站点信息后续取user里面的
        outScanReqDTO.setSiteCode(ArriveScanController.TEST_STIE_CODE);
        outScanReqDTO.setSiteName(ArriveScanController.TEST_STIE_NAME);

        DubboOutScanRespDTO dubboOutScanRespDTO = outScanService.outScan(outScanReqDTO);
        OutScanRespVO outScanRespVO = new OutScanRespVO();
        BeanUtil.copyProperties(dubboOutScanRespDTO, outScanRespVO);

        log.info("出货扫描响应：{}", JSON.toJSONString(outScanRespVO));
        return new WebResponse<>(outScanRespVO);
    }

    @PostMapping("/check")
    //@PreAuthorize("@ctsPermissionService.hasAnyPermission('out_scan')")
    @ZsmpApiOperation(description = "校验", name = "校验")
    public WebResponse<OutScanCheckRespVO> check(@Valid @RequestBody OutScanCheckReqVO checkReqVO){
        log.info("订单校验入参：{}", JSON.toJSONString(checkReqVO));

        // 转换请求参数
        DubboOutScanCheckReqDTO dubboScanCheckReqDTO = new DubboOutScanCheckReqDTO();
        BeanUtil.copyProperties(checkReqVO, dubboScanCheckReqDTO);
        dubboScanCheckReqDTO.setSiteCode(ArriveScanController.TEST_STIE_CODE);
        // 调用校验服务
        DubboOutScanCheckRespDTO dubboResult = outScanService.checkOrder(dubboScanCheckReqDTO);

        // 转换响应结果
        OutScanCheckRespVO result = convertToCheckRespVO(dubboResult);

        log.info("订单校验完成，单号数量：{}，需要二次确认：{}，错误数量：{}",
                checkReqVO.getNoList().size(),
                result.getNeedConfirm(),
                result.getErrors() != null ? result.getErrors().size() : 0);

        return new WebResponse<>(result);
    }

    @PostMapping("/page")
    //@PreAuthorize("@ctsPermissionService.hasAnyPermission('out_scan')")
    @ZsmpApiOperation(description = "查询", name = "查询")
    public WebResponse<PageResult<OutScanQueryRespVO>> page(@RequestBody PageQuery<OutScanQueryReqVO> pageRequest){
        log.info("出货扫描查询入参：{}", JSON.toJSONString(pageRequest));

        // 1. 转换请求参数
        PageQuery<DubboOutScanQueryReqDTO> dubboPageQuery = PageMapUtil.map(pageRequest, DubboOutScanQueryReqDTO.class);

        // 2. 调用查询服务
        PageResult<DubboOutScanQueryRespDTO> dubboResult = outScanService.page(dubboPageQuery);

        // 3. 转换响应结果
        PageResult<OutScanQueryRespVO> result = PageMapUtil.map(dubboResult, OutScanQueryRespVO.class);

        log.info("出货扫描查询完成，总记录数：{}", dubboResult.getTotal());
        return new WebResponse<>(result);
    }

    /**
     * 转换校验响应结果
     */
    private OutScanCheckRespVO convertToCheckRespVO(DubboOutScanCheckRespDTO dubboResult) {
        OutScanCheckRespVO result = new OutScanCheckRespVO();
        result.setNeedConfirm(dubboResult.getNeedConfirm());

        if (CollectionUtils.isNotEmpty(dubboResult.getErrors())) {
            List<ErrorInfo> errors = dubboResult.getErrors().stream()
                    .map(dubboError -> {
                        ErrorInfo errorVO = new ErrorInfo();
                        errorVO.setErrorMsg(dubboError.getErrorMsg());
                        errorVO.setBillNos(dubboError.getBillNos());
                        return errorVO;
                    })
                    .collect(java.util.stream.Collectors.toList());
            result.setErrors(errors);
        }

        return result;
    }
}
