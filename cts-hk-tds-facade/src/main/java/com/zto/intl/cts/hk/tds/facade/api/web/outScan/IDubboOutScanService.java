package com.zto.intl.cts.hk.tds.facade.api.web.outScan;

import com.zto.intl.cts.hk.core.domain.web.PageQuery;
import com.zto.intl.cts.hk.core.domain.web.PageResult;
import com.zto.intl.cts.hk.tds.facade.api.web.outScan.dto.request.DubboOutScanCheckReqDTO;
import com.zto.intl.cts.hk.tds.facade.api.web.outScan.dto.request.DubboOutScanQueryReqDTO;
import com.zto.intl.cts.hk.tds.facade.api.web.outScan.dto.request.DubboOutScanReqDTO;
import com.zto.intl.cts.hk.tds.facade.api.web.outScan.dto.response.DubboOutScanCheckRespDTO;
import com.zto.intl.cts.hk.tds.facade.api.web.outScan.dto.response.DubboOutScanQueryRespDTO;
import com.zto.intl.cts.hk.tds.facade.api.web.outScan.dto.response.DubboOutScanRespDTO;

/**
 * 出货扫描服务
 * <AUTHOR>
 * @date 20225/7/24
 */
public interface IDubboOutScanService {

    /**
     * 出货扫描
     */
    DubboOutScanRespDTO outScan(DubboOutScanReqDTO outScanReqDTO);

    /**
     * 查询出货扫描结果
     */
    PageResult<DubboOutScanQueryRespDTO> page(PageQuery<DubboOutScanQueryReqDTO> pageQuery);


    /**
     * 校验订单（支持批量，包含重量差异校验）
     */
    DubboOutScanCheckRespDTO checkOrder(DubboOutScanCheckReqDTO checkReqDTO);

}
