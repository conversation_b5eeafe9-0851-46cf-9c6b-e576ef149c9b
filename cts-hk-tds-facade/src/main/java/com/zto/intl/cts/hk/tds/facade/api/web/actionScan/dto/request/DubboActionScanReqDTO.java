package com.zto.intl.cts.hk.tds.facade.api.web.actionScan.dto.request;

import com.zto.intl.cts.hk.core.enums.ScanNoTypeEnum;
import com.zto.intl.cts.hk.core.enums.ScanTypeEnum;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 状态扫描请求
 * @Date 2025/7/29
 * @Version 1.0
 */
public class DubboActionScanReqDTO implements Serializable {
    private static final long serialVersionUID = 2352599016345775935L;

    private List<String> noList;
    private String actionCode;
    private ScanNoTypeEnum noTypeEnum;
    private ScanTypeEnum scanTypeEnum;
    private String userCode;
    private String siteCode;

    public ScanNoTypeEnum getNoTypeEnum() {
        return noTypeEnum;
    }

    public void setNoTypeEnum(ScanNoTypeEnum noTypeEnum) {
        this.noTypeEnum = noTypeEnum;
    }

    public ScanTypeEnum getScanTypeEnum() {
        return scanTypeEnum;
    }

    public void setScanTypeEnum(ScanTypeEnum scanTypeEnum) {
        this.scanTypeEnum = scanTypeEnum;
    }

    public List<String> getNoList() {
        return noList;
    }

    public void setNoList(List<String> noList) {
        this.noList = noList;
    }

    public String getActionCode() {
        return actionCode;
    }

    public void setActionCode(String actionCode) {
        this.actionCode = actionCode;
    }

    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public String getSiteCode() {
        return siteCode;
    }

    public void setSiteCode(String siteCode) {
        this.siteCode = siteCode;
    }
}
