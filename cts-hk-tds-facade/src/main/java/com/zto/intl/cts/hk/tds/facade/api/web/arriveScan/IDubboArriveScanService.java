package com.zto.intl.cts.hk.tds.facade.api.web.arriveScan;

import com.zto.intl.cts.hk.tds.facade.api.web.arriveScan.dto.request.DubboArriveScanQueryReqDTO;
import com.zto.intl.cts.hk.tds.facade.api.web.arriveScan.dto.request.DubboArriveScanReqDTO;
import com.zto.intl.cts.hk.tds.facade.api.web.arriveScan.dto.request.DubboArriveScanCheckReqDTO;
import com.zto.intl.cts.hk.tds.facade.api.web.arriveScan.dto.request.DubboScanWeightReqDTO;
import com.zto.intl.cts.hk.tds.facade.api.web.arriveScan.dto.response.DubboArriveScanQueryRespDTO;
import com.zto.intl.cts.hk.tds.facade.api.web.arriveScan.dto.response.DubboArriveScanRespDTO;
import com.zto.intl.cts.hk.core.domain.web.PageQuery;
import com.zto.intl.cts.hk.core.domain.web.PageResult;
import com.zto.intl.cts.hk.tds.facade.api.web.arriveScan.dto.response.DubboArriveScanCheckRespDTO;
/**
 * 到货扫描服务
 * <AUTHOR>
 * @date 20225/7/24
 */
public interface IDubboArriveScanService {

    /**
     * 到货扫描
     */
    DubboArriveScanRespDTO arriveScan(DubboArriveScanReqDTO arriveScanDTO);

    /**
     * 查询到货扫描结果
     */
    PageResult<DubboArriveScanQueryRespDTO> page(PageQuery<DubboArriveScanQueryReqDTO> pageQuery);

    /**
     * 称重/尺寸 更新
     */
    void updateWeightAndSize(DubboScanWeightReqDTO weightReqDTO);

    /**
     * 校验订单（支持批量，包含重量差异校验）
     */
    DubboArriveScanCheckRespDTO checkOrder(DubboArriveScanCheckReqDTO checkReqDTO);

}
