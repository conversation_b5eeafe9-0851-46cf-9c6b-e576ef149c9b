package com.zto.intl.cts.hk.tds.facade.api.web.actionScan.dto.request;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description 状态扫描分页查询请求
 * @Date 2025/8/1
 * @Version 1.0
 */
public class DubboActionScanPageReqDTO implements Serializable {

    private static final long serialVersionUID = 187832156529643656L;
    /**
     * 扫描开始时间
     */
    private Date startTime;

    /**
     * 扫描结束时间
     */
    private Date endTime;

    /**
     * 是否执行成功
     */
    private Boolean isSuccess;

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Boolean getSuccess() {
        return isSuccess;
    }

    public void setSuccess(Boolean success) {
        isSuccess = success;
    }


}
