package com.zto.intl.cts.hk.tds.facade.api.web.arriveScan.dto.request;

import com.zto.intl.cts.hk.core.enums.ScanNoTypeEnum;
import com.zto.intl.cts.hk.core.enums.ScanTypeEnum;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 到货扫描校验请求DTO
 * <AUTHOR>
 * @date 2025/8/1
 */
public class DubboArriveScanCheckReqDTO implements Serializable {
    private static final long serialVersionUID = 2321021199342634410L;

    /**
     * 单号类型
     * 运单号: BILL_CODE,
     * 客户单号：CUSTOMER_ORDER_NO
     */
    private ScanNoTypeEnum noTypeEnum;

    /** 单号列表（支持单个和批量） **/
    private List<String> noList;

    /** 称重重量（可选，用于重量差异校验） **/
    private BigDecimal weight;

    /** 扫描类型（可选，用于单个扫描时的额外业务规则校验） **/
    private ScanTypeEnum scanType;

    /** 操作人所属站点（可选，用于重复扫描校验） **/
    private String siteCode;

    public ScanNoTypeEnum getNoTypeEnum() {
        return noTypeEnum;
    }

    public void setNoTypeEnum(ScanNoTypeEnum noTypeEnum) {
        this.noTypeEnum = noTypeEnum;
    }

    public List<String> getNoList() {
        return noList;
    }

    public void setNoList(List<String> noList) {
        this.noList = noList;
    }

    public BigDecimal getWeight() {
        return weight;
    }

    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    public ScanTypeEnum getScanType() {
        return scanType;
    }

    public void setScanType(ScanTypeEnum scanType) {
        this.scanType = scanType;
    }

    public String getSiteCode() {
        return siteCode;
    }

    public void setSiteCode(String siteCode) {
        this.siteCode = siteCode;
    }
}
