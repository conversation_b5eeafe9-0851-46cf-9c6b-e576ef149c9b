package com.zto.intl.cts.hk.tds.facade.api.web.actionScan;

import com.zto.intl.cts.hk.core.domain.web.PageQuery;
import com.zto.intl.cts.hk.core.domain.web.PageResult;
import com.zto.intl.cts.hk.tds.facade.api.web.actionScan.dto.request.DubboActionScanPageReqDTO;
import com.zto.intl.cts.hk.tds.facade.api.web.actionScan.dto.request.DubboActionScanReqDTO;
import com.zto.intl.cts.hk.tds.facade.api.web.actionScan.dto.response.DubboActionScanPageRespDTO;
import com.zto.intl.cts.hk.tds.facade.api.web.actionScan.dto.response.DubboActionScanRespDTO;

/**
 * <AUTHOR>
 * @Description 状态扫描
 * @Date 2025/7/29
 * @Version 1.0
 */
public interface DubboActionScanService {

    void scan(DubboActionScanReqDTO reqDTO);

    DubboActionScanRespDTO scanBatch(DubboActionScanReqDTO reqDTO);

    PageResult<DubboActionScanPageRespDTO> page(PageQuery<DubboActionScanPageReqDTO> dto);
}
