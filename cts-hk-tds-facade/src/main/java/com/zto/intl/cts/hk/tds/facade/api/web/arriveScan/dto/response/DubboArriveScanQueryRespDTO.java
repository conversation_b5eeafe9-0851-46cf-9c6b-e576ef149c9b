package com.zto.intl.cts.hk.tds.facade.api.web.arriveScan.dto.response;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 到货扫描结果
 * <AUTHOR>
 * @date 2025/07/24
 */
public class DubboArriveScanQueryRespDTO implements Serializable {

    private static final long serialVersionUID = 2352020192345334535L;

    /** id **/
    private Long id;

    /** 序列 **/
    private Integer seq;

    /** 扫描时间 **/
    private Date operateTime;

    /** 运单号 **/
    private String billCode;

    /** 客户单号 **/
    private String customerOrderNo;

    /** 重量kg **/
    private BigDecimal weight;

    /** 长CM **/
    private BigDecimal length;

    /** 宽CM **/
    private BigDecimal width;

    /** 高CM **/
    private BigDecimal height;

    /** 支付状态 **/
    private Byte payStatus;

    /** 支付状态描述 **/
    private String payStatusDesc;

    /** 收件人名称 **/
    private String consigneeName;

    /** 收件人电话 **/
    private String consigneeMobile;

    /** 收件人详细地址 **/
    private String consigneeAddress;

    /** 末端派送方式 0: 派送 1:自提 **/
    private Integer deliveryMethod;

    /** 末端派送方式描述 **/
    private String deliveryMethodDesc;

    /** 扫描结果 **/
    private Boolean isSuccess;

    /** 扫描结果描述 **/
    private String scanResultDesc;

    /** 扫描失败原因 **/
    private String failMessage;

    /** 揽收人 **/
    private String operator;

    public Integer getSeq() {
        return seq;
    }

    public void setSeq(Integer seq) {
        this.seq = seq;
    }

    public Date getOperateTime() {
        return operateTime;
    }

    public void setOperateTime(Date operateTime) {
        this.operateTime = operateTime;
    }

    public String getBillCode() {
        return billCode;
    }

    public void setBillCode(String billCode) {
        this.billCode = billCode;
    }

    public String getCustomerOrderNo() {
        return customerOrderNo;
    }

    public void setCustomerOrderNo(String customerOrderNo) {
        this.customerOrderNo = customerOrderNo;
    }

    public BigDecimal getWeight() {
        return weight;
    }

    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    public BigDecimal getLength() {
        return length;
    }

    public void setLength(BigDecimal length) {
        this.length = length;
    }

    public BigDecimal getWidth() {
        return width;
    }

    public void setWidth(BigDecimal width) {
        this.width = width;
    }

    public BigDecimal getHeight() {
        return height;
    }

    public void setHeight(BigDecimal height) {
        this.height = height;
    }

    public Byte getPayStatus() {
        return payStatus;
    }

    public void setPayStatus(Byte payStatus) {
        this.payStatus = payStatus;
    }

    public String getPayStatusDesc() {
        return payStatusDesc;
    }

    public void setPayStatusDesc(String payStatusDesc) {
        this.payStatusDesc = payStatusDesc;
    }

    public String getConsigneeName() {
        return consigneeName;
    }

    public void setConsigneeName(String consigneeName) {
        this.consigneeName = consigneeName;
    }

    public String getConsigneeMobile() {
        return consigneeMobile;
    }

    public void setConsigneeMobile(String consigneeMobile) {
        this.consigneeMobile = consigneeMobile;
    }

    public String getConsigneeAddress() {
        return consigneeAddress;
    }

    public void setConsigneeAddress(String consigneeAddress) {
        this.consigneeAddress = consigneeAddress;
    }

    public Integer getDeliveryMethod() {
        return deliveryMethod;
    }

    public void setDeliveryMethod(Integer deliveryMethod) {
        this.deliveryMethod = deliveryMethod;
    }

    public String getDeliveryMethodDesc() {
        return deliveryMethodDesc;
    }

    public void setDeliveryMethodDesc(String deliveryMethodDesc) {
        this.deliveryMethodDesc = deliveryMethodDesc;
    }

    public Boolean getIsSuccess() {
        return isSuccess;
    }

    public void setIsSuccess(Boolean success) {
        isSuccess = success;
    }

    public String getScanResultDesc() {
        return scanResultDesc;
    }

    public void setScanResultDesc(String scanResultDesc) {
        this.scanResultDesc = scanResultDesc;
    }

    public String getFailMessage() {
        return failMessage;
    }

    public void setFailMessage(String failMessage) {
        this.failMessage = failMessage;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
}
