<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.zto</groupId>
    <artifactId>cts-hk</artifactId>
    <packaging>pom</packaging>
    <version>1.0.1-SNAPSHOT</version>

    <modules>
        <module>cts-hk-api</module>
        <module>cts-hk-commons</module>
        <module>cts-hk-web</module>
        <module>cts-hk-core</module>

    </modules>

    <parent>
        <groupId>com.zto.titans</groupId>
        <artifactId>titans-parent</artifactId>
        <version>2.9.3.13.RELEASE</version>
    </parent>

    <!-- 统一配置 Java 版本 -->
    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <cts.hk.version>1.0.1-SNAPSHOT</cts.hk.version>
        <intl.common.version>0.0.59-SNAPSHOT</intl.common.version>
        <zsmp.version>0.0.1-SNAPSHOT</zsmp.version>
        <hibernate.validator>6.2.5.Final</hibernate.validator>
        <fastjson.version>1.2.83</fastjson.version>
        <easyexcel.version>3.3.3</easyexcel.version>
        <servlet-api.version>4.0.1</servlet-api.version>
        <orika.version>1.5.4</orika.version>
        <poi.version>4.1.2</poi.version>
    </properties>


    <dependencyManagement>
        <dependencies>

            <dependency>
                <groupId>com.zto</groupId>
                <artifactId>cts-hk-commons</artifactId>
                <version>${cts.hk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zto</groupId>
                <artifactId>cts-hk-task-mq</artifactId>
                <version>${cts.hk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zto</groupId>
                <artifactId>cts-hk-base-facade</artifactId>
                <version>${cts.hk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zto</groupId>
                <artifactId>cts-hk-transfer-facade</artifactId>
                <version>${cts.hk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zto</groupId>
                <artifactId>cts-hk-tds-facade</artifactId>
                <version>${cts.hk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zto</groupId>
                <artifactId>cts-hk-core</artifactId>
                <version>${cts.hk.version}</version>
            </dependency>

            <dependency>
                <groupId>com.zto.intl</groupId>
                <artifactId>zto-international-common</artifactId>
                <version>${intl.common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.ben-manes.caffeine</groupId>
                <artifactId>caffeine</artifactId>
                <version>2.8.2</version>
            </dependency>

            <dependency>
                <groupId>com.zto.zsmp</groupId>
                <artifactId>zsmp-annotation</artifactId>
                <version>${zsmp.version}</version>
            </dependency>

            <dependency>
                <groupId>org.hibernate.validator</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>${hibernate.validator}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>javax.servlet-api</artifactId>
                <version>${servlet-api.version}</version>
            </dependency>
            <dependency>
                <groupId>ma.glasnost.orika</groupId>
                <artifactId>orika-core</artifactId>
                <version>${orika.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>


</project>
