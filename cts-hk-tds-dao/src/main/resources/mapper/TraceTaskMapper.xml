<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.intl.cts.hk.tds.dao.mapper.TraceTaskMapper">
  <resultMap id="BaseResultMap" type="com.zto.intl.cts.hk.tds.dao.entity.TraceTask">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="bill_code" jdbcType="VARCHAR" property="billCode" />
    <result column="customer_order_no" jdbcType="VARCHAR" property="customerOrderNo" />
    <result column="action_code" jdbcType="VARCHAR" property="actionCode" />
    <result column="action_name" jdbcType="VARCHAR" property="actionName" />
    <result column="trace_action_code" jdbcType="VARCHAR" property="traceActionCode" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="operate_time" jdbcType="TIMESTAMP" property="operateTime" />
    <result column="site_code" jdbcType="VARCHAR" property="siteCode" />
    <result column="site_name" jdbcType="VARCHAR" property="siteName" />
    <result column="country_code" jdbcType="VARCHAR" property="countryCode" />
    <result column="country" jdbcType="VARCHAR" property="country" />
    <result column="message" jdbcType="VARCHAR" property="message" />
    <result column="is_success" jdbcType="BIT" property="isSuccess" />
    <result column="push_time" jdbcType="TIMESTAMP" property="pushTime" />
    <result column="next_execute_time" jdbcType="TIMESTAMP" property="nextExecuteTime" />
    <result column="push_count" jdbcType="TINYINT" property="pushCount" />
    <result column="push_result" jdbcType="VARCHAR" property="pushResult" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
  </resultMap>
</mapper>