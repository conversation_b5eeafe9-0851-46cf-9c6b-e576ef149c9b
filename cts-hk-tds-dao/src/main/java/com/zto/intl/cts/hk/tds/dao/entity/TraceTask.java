package com.zto.intl.cts.hk.tds.dao.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter
@Setter
@ToString
@Accessors(chain = true)
@Table(name = "`trace_task`")
public class TraceTask implements Serializable {
    /**
     * 主键id
     */
    @Id
    @Column(name = "`id`")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 运单号
     */
    @Column(name = "`bill_code`")
    private String billCode;

    /**
     * 客户订单号
     */
    @Column(name = "`customer_order_no`")
    private String customerOrderNo;

    /**
     * 操作状态码
     */
    @Column(name = "`action_code`")
    private String actionCode;

    /**
     * 操作状态描述
     */
    @Column(name = "`action_name`")
    private String actionName;

    /**
     * 轨迹状态码
     */
    @Column(name = "`trace_action_code`")
    private String traceActionCode;

    /**
     * 操作人
     */
    @Column(name = "`operator`")
    private String operator;

    /**
     * 手机号
     */
    @Column(name = "`mobile`")
    private String mobile;

    /**
     * 操作时间
     */
    @Column(name = "`operate_time`")
    private Date operateTime;

    /**
     * 站点编码
     */
    @Column(name = "`site_code`")
    private String siteCode;

    /**
     * 站点名称
     */
    @Column(name = "`site_name`")
    private String siteName;

    /**
     * 国家、地区编码
     */
    @Column(name = "`country_code`")
    private String countryCode;

    /**
     * 国家
     */
    @Column(name = "`country`")
    private String country;

    /**
     * 轨迹描述
     */
    @Column(name = "`message`")
    private String message;

    /**
     * 是否执行成功, 0 否 1 是
     */
    @Column(name = "`is_success`")
    private Boolean isSuccess;

    /**
     * 推送时间
     */
    @Column(name = "`push_time`")
    private Date pushTime;

    /**
     * 下次执行时间
     */
    @Column(name = "`next_execute_time`")
    private Date nextExecuteTime;

    /**
     * 推送次数；超过上限次数后，不再推送
     */
    @Column(name = "`push_count`")
    private Integer pushCount;

    /**
     * 推送结果
     */
    @Column(name = "`push_result`")
    private String pushResult;

    /**
     * 创建时间
     */
    @Column(name = "`gmt_create`")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @Column(name = "`gmt_modified`")
    private Date gmtModified;

    /**
     * 创建人
     */
    @Column(name = "`creator`")
    private String creator;

    /**
     * 修改人
     */
    @Column(name = "`modifier`")
    private String modifier;

    public static final String ID = "id";

    public static final String DB_ID = "id";

    public static final String BILL_CODE = "billCode";

    public static final String DB_BILL_CODE = "bill_code";

    public static final String CUSTOMER_ORDER_NO = "customerOrderNo";

    public static final String DB_CUSTOMER_ORDER_NO = "customer_order_no";

    public static final String ACTION_CODE = "actionCode";

    public static final String DB_ACTION_CODE = "action_code";

    public static final String ACTION_NAME = "actionName";

    public static final String DB_ACTION_NAME = "action_name";

    public static final String TRACE_ACTION_CODE = "traceActionCode";

    public static final String DB_TRACE_ACTION_CODE = "trace_action_code";

    public static final String OPERATOR = "operator";

    public static final String DB_OPERATOR = "operator";

    public static final String MOBILE = "mobile";

    public static final String DB_MOBILE = "mobile";

    public static final String OPERATE_TIME = "operateTime";

    public static final String DB_OPERATE_TIME = "operate_time";

    public static final String SITE_CODE = "siteCode";

    public static final String DB_SITE_CODE = "site_code";

    public static final String SITE_NAME = "siteName";

    public static final String DB_SITE_NAME = "site_name";

    public static final String COUNTRY_CODE = "countryCode";

    public static final String DB_COUNTRY_CODE = "country_code";

    public static final String COUNTRY = "country";

    public static final String DB_COUNTRY = "country";

    public static final String MESSAGE = "message";

    public static final String DB_MESSAGE = "message";

    public static final String IS_SUCCESS = "isSuccess";

    public static final String DB_IS_SUCCESS = "is_success";

    public static final String PUSH_TIME = "pushTime";

    public static final String DB_PUSH_TIME = "push_time";

    public static final String NEXT_EXECUTE_TIME = "nextExecuteTime";

    public static final String DB_NEXT_EXECUTE_TIME = "next_execute_time";

    public static final String PUSH_COUNT = "pushCount";

    public static final String DB_PUSH_COUNT = "push_count";

    public static final String PUSH_RESULT = "pushResult";

    public static final String DB_PUSH_RESULT = "push_result";

    public static final String GMT_CREATE = "gmtCreate";

    public static final String DB_GMT_CREATE = "gmt_create";

    public static final String GMT_MODIFIED = "gmtModified";

    public static final String DB_GMT_MODIFIED = "gmt_modified";

    public static final String CREATOR = "creator";

    public static final String DB_CREATOR = "creator";

    public static final String MODIFIER = "modifier";

    public static final String DB_MODIFIER = "modifier";

    private static final long serialVersionUID = 1L;
}
